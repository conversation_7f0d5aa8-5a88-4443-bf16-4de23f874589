{"configurations": [{"name": "Template-FreeRTOS", "includePath": ["e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\ApplicationLayer\\User", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\HardwareLayer\\CMSIS\\Device\\ST\\STM32F4xx\\Include", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\HardwareLayer\\CMSIS\\Include", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\HardwareLayer\\CMSIS\\Device\\ST\\STM32F4xx\\Source\\arm", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\HardwareLayer\\STM32F4xx_StdPeriph_Driver\\inc", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\DriverLayer\\Led", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\DriverLayer\\SystemClock", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\DriverLayer\\Usart", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\DriverLayer\\TIM", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\DriverLayer\\IO", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Kernel\\include", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Kernel\\portable\\RVDS\\ARM_CM4F", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Task", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Plus-Trace\\Include", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Plus-Trace\\config", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Plus-Trace\\streamports\\Jlink_RTT\\include", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\ApplicationLayer\\uORB", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\ApplicationLayer\\uORB\\DriverFramework", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\ApplicationLayer\\uORB\\topics", "D:\\MDK3.2\\ARM\\ARMCC\\include", "D:\\MDK3.2\\ARM\\ARMCC\\include\\rw", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\HardwareLayer\\CMSIS\\Device\\ST\\STM32F4xx\\Source", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\HardwareLayer\\STM32F4xx_StdPeriph_Driver\\src", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Plus-Trace", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Plus-Trace\\streamports\\Jlink_RTT", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Kernel", "e:\\STM32项目\\F4项目文件-新\\FreeRTOS\\Motion_Control\\SourceCode\\OSLayer\\FreeRTOS-Kernel\\portable\\MemMang"], "defines": ["STM32F40_41xxx", "USE_STDPERIPH_DRIVER", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"], "intelliSenseMode": "${default}"}], "version": 4}