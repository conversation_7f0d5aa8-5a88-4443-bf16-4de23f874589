<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\Template-FreeRTOS.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\Template-FreeRTOS.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Sat Aug 05 09:52:10 2023
<BR><P>
<H3>Maximum Stack Usage =        704 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
prvTimerTask &rArr; prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[cd]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[193]">prvTraceStoreSimpleStringEventHelper</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[103]">vTraceStop</a><BR>
 <LI><a href="#[191]">prvTraceError</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[17d]">prvSetRecorderEnabled</a><BR>
 <LI><a href="#[2d]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2d]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[2d]">ADC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[15]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2f]">CAN1_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[30]">CAN1_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[31]">CAN1_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2e]">CAN1_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5b]">CAN2_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5c]">CAN2_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5d]">CAN2_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5a]">CAN2_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6a]">CRYP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[69]">DCMI_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[26]">DMA1_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[27]">DMA1_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[28]">DMA1_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[29]">DMA1_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2a]">DMA1_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2b]">DMA1_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2c]">DMA1_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4a]">DMA1_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[53]">DMA2_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[54]">DMA2_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[55]">DMA2_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[56]">DMA2_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[57]">DMA2_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5f]">DMA2_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[60]">DMA2_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[61]">DMA2_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[18]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[58]">ETH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[59]">ETH_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[21]">EXTI0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[43]">EXTI15_10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[22]">EXTI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[23]">EXTI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[24]">EXTI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[25]">EXTI4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[32]">EXTI9_5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1f]">FLASH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6c]">FPU_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4b]">FSMC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6b]">HASH_RNG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[13]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3b]">I2C1_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3a]">I2C1_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3d]">I2C2_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3c]">I2C2_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[64]">I2C3_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[63]">I2C3_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[14]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[12]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5e]">OTG_FS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[45]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[66]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[65]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[68]">OTG_HS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[67]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1c]">PVD_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[19]">PendSV_Handler</a> from port.o(.emb_text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[20]">RCC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[44]">RTC_Alarm_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1e]">RTC_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[11]">Reset_Handler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4c]">SDIO_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3e]">SPI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3f]">SPI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4e]">SPI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[17]">SVC_Handler</a> from port.o(.emb_text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1a]">SysTick_Handler</a> from systemclock.o(i.SysTick_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6d]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[1d]">TAMP_STAMP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[33]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[36]">TIM1_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[35]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[34]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[37]">TIM2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[38]">TIM3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[39]">TIM4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4d]">TIM5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[51]">TIM6_DAC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[52]">TIM7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[46]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[49]">TIM8_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[48]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[47]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[7a]">TzCtrl</a> from trckernelport.o(i.TzCtrl) referenced from trckernelport.o(i.vTraceEnable)
 <LI><a href="#[4f]">UART4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[50]">UART5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[40]">USART1_IRQHandler</a> from usart.o(i.USART1_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[41]">USART2_IRQHandler</a> from usart.o(i.USART2_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[42]">USART3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[62]">USART6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[16]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1b]">WWDG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[75]">task_start(void*)</a> from task_start.o(i._Z10task_startPv) referenced from task_start.o(i.Create_task_start)
 <LI><a href="#[74]">task_Sensor(void*)</a> from task_sensor.o(i._Z11task_SensorPv) referenced from task_sensor.o(i.Create_task_sensor)
 <LI><a href="#[73]">task_control(void*)</a> from task_control.o(i._Z12task_controlPv) referenced from task_control.o(i.Create_task_control)
 <LI><a href="#[77]">__default_new_handler()</a> from arm_newhandler_noexceptions.o(.text) referenced from new.o(i._Znwj)
 <LI><a href="#[76]">task_test(void*)</a> from task_test.o(i._Z9task_testPv) referenced from task_test.o(i.Create_task_test)
 <LI><a href="#[72]">[local to arm_exceptions_c]::__default_terminate_handler()</a> from arm_exceptions_globs.o(.text) referenced from arm_exceptions_globs.o(.text)
 <LI><a href="#[7f]">uORB::DeviceNode::open(device::file_t*)</a> from uorbdevices.o(i._ZN4uORB10DeviceNode4openEPN6device6file_tE) referenced from uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
 <LI><a href="#[80]">uORB::DeviceNode::close(device::file_t*)</a> from uorbdevices.o(i._ZN4uORB10DeviceNode5closeEPN6device6file_tE) referenced from uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
 <LI><a href="#[82]">uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long)</a> from uorbdevices.o(i._ZN4uORB10DeviceNode5ioctlEPN6device6file_tEim) referenced from uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
 <LI><a href="#[81]">uORB::DeviceNode::write(device::file_t*, const char*, unsigned)</a> from uorbdevices.o(i._ZN4uORB10DeviceNode5writeEPN6device6file_tEPKcj) referenced from uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
 <LI><a href="#[7d]">uORB::DeviceNode::~DeviceNode__deallocating()</a> from uorbdevices.o(i._ZN4uORB10DeviceNodeD0Ev) referenced from uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
 <LI><a href="#[7c]">uORB::DeviceNode::~DeviceNode()</a> from uorbdevices.o(i._ZN4uORB10DeviceNodeD1Ev) referenced from uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
 <LI><a href="#[87]">uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)</a> from uorbdevices.o(i._ZN4uORB12DeviceMaster5ioctlEPN6device6file_tEim) referenced from uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
 <LI><a href="#[84]">uORB::DeviceMaster::~DeviceMaster__deallocating()</a> from uorbdevices.o(i._ZN4uORB12DeviceMasterD0Ev) referenced from uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
 <LI><a href="#[83]">uORB::DeviceMaster::~DeviceMaster()</a> from uorbdevices.o(i._ZN4uORB12DeviceMasterD1Ev) referenced from uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
 <LI><a href="#[7e]">device::VDev::init()</a> from vdev.o(i._ZN6device4VDev4initEv) referenced from uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
 <LI><a href="#[7e]">device::VDev::init()</a> from vdev.o(i._ZN6device4VDev4initEv) referenced from uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
 <LI><a href="#[7e]">device::VDev::init()</a> from vdev.o(i._ZN6device4VDev4initEv) referenced from vdev.o(.constdata__ZTVN6device4VDevE)
 <LI><a href="#[7e]">device::VDev::init()</a> from vdev.o(i._ZN6device4VDev4initEv) referenced from vdev.o(.constdata__ZTVN6device5VFileE)
 <LI><a href="#[85]">device::VDev::open(device::file_t*)</a> from vdev.o(i._ZN6device4VDev4openEPNS_6file_tE) referenced from uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
 <LI><a href="#[85]">device::VDev::open(device::file_t*)</a> from vdev.o(i._ZN6device4VDev4openEPNS_6file_tE) referenced from vdev.o(.constdata__ZTVN6device4VDevE)
 <LI><a href="#[85]">device::VDev::open(device::file_t*)</a> from vdev.o(i._ZN6device4VDev4openEPNS_6file_tE) referenced from vdev.o(.constdata__ZTVN6device5VFileE)
 <LI><a href="#[86]">device::VDev::close(device::file_t*)</a> from vdev.o(i._ZN6device4VDev5closeEPNS_6file_tE) referenced from uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
 <LI><a href="#[86]">device::VDev::close(device::file_t*)</a> from vdev.o(i._ZN6device4VDev5closeEPNS_6file_tE) referenced from vdev.o(.constdata__ZTVN6device4VDevE)
 <LI><a href="#[86]">device::VDev::close(device::file_t*)</a> from vdev.o(i._ZN6device4VDev5closeEPNS_6file_tE) referenced from vdev.o(.constdata__ZTVN6device5VFileE)
 <LI><a href="#[89]">device::VDev::ioctl(device::file_t*, int, unsigned long)</a> from vdev.o(i._ZN6device4VDev5ioctlEPNS_6file_tEim) referenced from vdev.o(.constdata__ZTVN6device4VDevE)
 <LI><a href="#[89]">device::VDev::ioctl(device::file_t*, int, unsigned long)</a> from vdev.o(i._ZN6device4VDev5ioctlEPNS_6file_tEim) referenced from vdev.o(.constdata__ZTVN6device5VFileE)
 <LI><a href="#[88]">device::VDev::~VDev()</a> from vdev.o(i._ZN6device4VDevD1Ev) referenced from vdev.o(.constdata__ZTVN6device4VDevE)
 <LI><a href="#[8b]">device::Device::init()</a> from vdev.o(i._ZN6device6Device4initEv) referenced from vdev.o(.constdata__ZTVN6device6DeviceE)
 <LI><a href="#[8a]">device::Device::~Device()</a> from vdev.o(i._ZN6device6DeviceD1Ev) referenced from vdev.o(.constdata__ZTVN6device6DeviceE)
 <LI><a href="#[8e]">__main</a> from __main.o(!!!main) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[8c]">__sti___13_task_test_cpp_27549090</a> from task_test.o(i.__sti___13_task_test_cpp_27549090) referenced from task_test.o(.init_array)
 <LI><a href="#[8d]">__sti___16_task_Control_cpp_97739fc6</a> from task_control.o(i.__sti___16_task_Control_cpp_97739fc6) referenced from task_control.o(.init_array)
 <LI><a href="#[70]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[6f]">_snputc</a> from _snputc.o(.text) referenced from __2snprintf.o(.text)
 <LI><a href="#[71]">fputc</a> from usart.o(i.fputc) referenced from _printf_char_file.o(.text)
 <LI><a href="#[79]">prvIdleTask</a> from tasks.o(i.prvIdleTask) referenced from tasks.o(i.vTaskStartScheduler)
 <LI><a href="#[78]">prvTaskExitError</a> from port.o(i.prvTaskExitError) referenced from port.o(i.pxPortInitialiseStack)
 <LI><a href="#[7b]">prvTimerTask</a> from timers.o(i.prvTimerTask) referenced from timers.o(i.xTimerCreateTimerTask)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[8e]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[90]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[92]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[1bb]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[1bc]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[1bd]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[1be]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[1bf]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[93]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[b4]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[95]"></a>_printf_s</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_s.o(.ARM.Collect$$_printf_percent$$00000014))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = _printf_s &rArr; _printf_string &rArr; _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
</UL>

<P><STRONG><a name="[1c0]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[9f]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[97]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[99]"></a>__rt_lib_init_heap_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000005))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_heap_2 &rArr; _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[1c1]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[1c2]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[1c3]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[1c4]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[1c5]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[9b]"></a>__rt_lib_init_cpp_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000031))
<BR><BR>[Stack]<UL><LI>Max Depth = 16 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_cpp_2 &rArr; __cpp_initialize__aeabi_
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__cpp_initialize__aeabi_
</UL>

<P><STRONG><a name="[1c6]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[1c7]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[1c8]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[1c9]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[1ca]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[1cb]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[1cc]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[1cd]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[1ce]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[1cf]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[1d0]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[1d1]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[1d2]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[1d3]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[1d4]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[a4]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
</UL>

<P><STRONG><a name="[1d5]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[1d6]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[1d7]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[1d8]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[1d9]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[1da]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[1db]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[1dc]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[91]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[1dd]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[9c]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[9e]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[1de]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[a0]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 696 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; Create_task_start &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[1df]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[ce]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[a3]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[1e0]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[a5]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[17]"></a>SVC_Handler</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1b3]"></a>__asm___6_port_c_39a90d8d__prvStartFirstTask</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[1b2]"></a>__asm___6_port_c_39a90d8d__prvEnableVFP</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[19]"></a>PendSV_Handler</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Stack]<UL><LI>Max Depth = 416 + Unknown Stack Size
<LI>Call Chain = PendSV_Handler &rArr; vTaskSwitchContext &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1ab]"></a>vPortGetIPSR</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, port.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
</UL>

<P><STRONG><a name="[11]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1e1]"></a>_maybe_terminate_alloc</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, maybetermalloc1.o(.emb_text), UNUSED)

<P><STRONG><a name="[2d]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[cd]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1e2]"></a>__use_no_semihosting</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi_2.o(.text), UNUSED)

<P><STRONG><a name="[12d]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_init
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::get_device_master(uORB::Flavor)
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int)
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_advertise(const orb_metadata*, int*, int)
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::publish(const orb_metadata*, void*, const void*)
</UL>

<P><STRONG><a name="[1e3]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[1e4]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[a8]"></a>malloc</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, h1_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strdup(const char*)
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_exceptions_buffer_init
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__cxa_get_globals
</UL>

<P><STRONG><a name="[ab]"></a>free</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, h1_free.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = free
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::ORBMap::~ORBMap()
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator delete (void*)
</UL>

<P><STRONG><a name="[ac]"></a>__2printf</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, __2printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_test
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_sensor
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_control
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_start
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTaskExitError
</UL>

<P><STRONG><a name="[ae]"></a>__2snprintf</STRONG> (Thumb, 50 bytes, Stack size 40 bytes, __2snprintf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Utils::node_mkpath(char*, uORB::Flavor, const orb_metadata*, int*)
</UL>

<P><STRONG><a name="[c2]"></a>_printf_str</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, _printf_str.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = _printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[94]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[b2]"></a>__printf</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, __printf_flags_ss_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[111]"></a>strcpy</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, strcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_DoInit
</UL>

<P><STRONG><a name="[116]"></a>strlen</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, strlen.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strdup(const char*)
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
</UL>

<P><STRONG><a name="[119]"></a>strncmp</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, strncmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_open(const char*, int, ...)
</UL>

<P><STRONG><a name="[b8]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_ReadNoLock
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteNoCheck
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteBlocking
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::write(device::file_t*, const char*, unsigned)
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcpy
</UL>

<P><STRONG><a name="[b5]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[1e5]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[b7]"></a>memcpy</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, memcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = memcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strdup(const char*)
</UL>

<P><STRONG><a name="[b9]"></a>__aeabi_memset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, aeabi_memset.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[da]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Receive_OneByte
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DataBuf_Decode
</UL>

<P><STRONG><a name="[bb]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[ba]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[1e6]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1e7]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1e8]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[bc]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[132]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::register_driver(const char*, void*)
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::getDev(const char*)
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::getDeviceNodeLocked(const char*)
</UL>

<P><STRONG><a name="[1e9]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[4]"></a>__rt_heap_escrow</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[3]"></a>__rt_heap_expand</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[8f]"></a>__cpp_initialize__aeabi_</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, init_aeabi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __cpp_initialize__aeabi_
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_cpp_2
</UL>

<P><STRONG><a name="[1ea]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[cc]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1eb]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[1ec]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1ed]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[a9]"></a>__rt_heap_descriptor</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_heap_descriptor_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[151]"></a>__rt_new_handler_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_new_handler_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
</UL>

<P><STRONG><a name="[1ee]"></a>__use_no_heap</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[1ef]"></a>__heap$guard</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, hguard.o(.text), UNUSED)

<P><STRONG><a name="[f]"></a>_terminate_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[6]"></a>_init_user_alloc</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)

<P><STRONG><a name="[aa]"></a>__Heap_Full</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, init_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
</UL>
<BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>

<P><STRONG><a name="[be]"></a>__Heap_Broken</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, init_alloc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[9a]"></a>_init_alloc</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, init_alloc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _init_alloc &rArr; __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_ProvideMemory
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Initialize
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_heap_descriptor
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_heap_2
</UL>

<P><STRONG><a name="[c0]"></a>__Heap_Initialize</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, h1_init.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
</UL>

<P><STRONG><a name="[5]"></a>__Heap_DescSize</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, h1_init.o(.text), UNUSED)

<P><STRONG><a name="[b1]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[af]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[b0]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>

<P><STRONG><a name="[6f]"></a>_snputc</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _snputc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __2snprintf.o(.text)
</UL>
<P><STRONG><a name="[c1]"></a>_printf_cs_common</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_str
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_string
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char
</UL>

<P><STRONG><a name="[c3]"></a>_printf_char</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, _printf_char.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>

<P><STRONG><a name="[96]"></a>_printf_string</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, _printf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_string &rArr; _printf_cs_common &rArr; _printf_str
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_cs_common
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_s
</UL>

<P><STRONG><a name="[ad]"></a>_printf_char_file</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, _printf_char_file.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120 + Unknown Stack Size
<LI>Call Chain = _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ferror
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[b6]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[1f0]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1f1]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1f2]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[77]"></a>__default_new_handler()</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, arm_newhandler_noexceptions.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = __default_new_handler() &rArr; std::terminate() &rArr; __cxa_get_globals &rArr; __ARM_exceptions_buffer_init &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std::terminate()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> new.o(i._Znwj)
</UL>
<P><STRONG><a name="[bd]"></a>__Heap_ProvideMemory</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, h1_extend.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Full
</UL>

<P><STRONG><a name="[c4]"></a>ferror</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ferror.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[bf]"></a>__rt_SIGRTMEM</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_rtmem_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGRTMEM &rArr; __rt_SIGRTMEM_inner &rArr; __default_signal_display
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_init_alloc
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__Heap_Broken
</UL>

<P><STRONG><a name="[c5]"></a>std::terminate()</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, arm_exceptions_terminate.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = std::terminate() &rArr; __cxa_get_globals &rArr; __ARM_exceptions_buffer_init &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__cxa_get_globals
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_new_handler()
</UL>

<P><STRONG><a name="[c8]"></a>__cxa_get_globals</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, arm_exceptions_globs.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72 + Unknown Stack Size
<LI>Call Chain = __cxa_get_globals &rArr; __ARM_exceptions_buffer_init &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_exceptions_buffer_init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_eh_globals_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std::terminate()
</UL>

<P><STRONG><a name="[9d]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[ca]"></a>__rt_eh_globals_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_eh_globals_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__cxa_get_globals
</UL>

<P><STRONG><a name="[a2]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[c9]"></a>abort</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, abort.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__cxa_get_globals
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;[local to arm_exceptions_c]::__default_terminate_handler()
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;std::terminate()
</UL>

<P><STRONG><a name="[c7]"></a>__sig_exit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, defsig_exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT
</UL>

<P><STRONG><a name="[c6]"></a>__rt_SIGRTMEM_inner</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, defsig_rtmem_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __rt_SIGRTMEM_inner &rArr; __default_signal_display
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM
</UL>

<P><STRONG><a name="[cb]"></a>__ARM_exceptions_buffer_init</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, arm_exceptions_mem.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __ARM_exceptions_buffer_init &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__cxa_get_globals
</UL>

<P><STRONG><a name="[1f3]"></a>__ARM_exceptions_buffer_allocate</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, arm_exceptions_mem.o(.text), UNUSED)

<P><STRONG><a name="[1f4]"></a>__ARM_exceptions_buffer_free</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, arm_exceptions_mem.o(.text), UNUSED)

<P><STRONG><a name="[d0]"></a>__default_signal_display</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, defsig_general.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __default_signal_display
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ttywrch
</UL>
<BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT_inner
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGRTMEM_inner
</UL>

<P><STRONG><a name="[cf]"></a>__rt_SIGABRT</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_abrt_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT_inner
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
</UL>

<P><STRONG><a name="[d2]"></a>__rt_SIGABRT_inner</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, defsig_abrt_inner.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __rt_SIGABRT_inner &rArr; __default_signal_display
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_SIGABRT
</UL>

<P><STRONG><a name="[15]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[d3]"></a>Create_task_control</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, task_control.o(i.Create_task_control))
<BR><BR>[Stack]<UL><LI>Max Depth = 656 + Unknown Stack Size
<LI>Call Chain = Create_task_control &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::CreateOtherTask()
</UL>

<P><STRONG><a name="[d5]"></a>Create_task_sensor</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, task_sensor.o(i.Create_task_sensor))
<BR><BR>[Stack]<UL><LI>Max Depth = 656 + Unknown Stack Size
<LI>Call Chain = Create_task_sensor &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::CreateOtherTask()
</UL>

<P><STRONG><a name="[d6]"></a>Create_task_start</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, task_start.o(i.Create_task_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 696 + Unknown Stack Size
<LI>Call Chain = Create_task_start &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d8]"></a>Create_task_test</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, task_test.o(i.Create_task_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 656 + Unknown Stack Size
<LI>Call Chain = Create_task_test &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::CreateOtherTask()
</UL>

<P><STRONG><a name="[d9]"></a>DataBuf_Decode</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, comm.o(i.DataBuf_Decode))
<BR><BR>[Stack]<UL><LI>Max Depth = 536 + Unknown Stack Size
<LI>Call Chain = DataBuf_Decode &rArr; xQueueGenericSendFromISR &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Receive_OneByte
</UL>

<P><STRONG><a name="[18]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[dc]"></a>FreeRTOS_GetTick</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, systemclock.o(i.FreeRTOS_GetTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = FreeRTOS_GetTick
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetAbsoluteTime_us
</UL>

<P><STRONG><a name="[de]"></a>GPIOA_init</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, tim.o(i.GPIOA_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = GPIOA_init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::init()
</UL>

<P><STRONG><a name="[e0]"></a>GPIO_Init</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PositionSwitch_init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOA_init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[fb]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 70 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(i.GPIO_PinAFConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[144]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit))
<BR><BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sensor::run()
</UL>

<P><STRONG><a name="[e1]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_ResetBits))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOA_init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>

<P><STRONG><a name="[e2]"></a>GetAbsoluteTime_us</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, systemclock.o(i.GetAbsoluteTime_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = GetAbsoluteTime_us &rArr; __aeabi_f2ulz
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_GetTick
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ul2f
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::write(device::file_t*, const char*, unsigned)
</UL>

<P><STRONG><a name="[13]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[e5]"></a>LED_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, led.o(i.LED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LED_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[14]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[e6]"></a>My_SysTick_Init</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, systemclock.o(i.My_SysTick_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = My_SysTick_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[12]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1a8]"></a>NVIC_Init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, misc.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[152]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, misc.o(i.NVIC_PriorityGroupConfig))
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[e8]"></a>PositionSwitch_init</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, io.o(i.PositionSwitch_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = PositionSwitch_init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sensor::init()
</UL>

<P><STRONG><a name="[df]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PositionSwitch_init
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOA_init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[fa]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_init
</UL>

<P><STRONG><a name="[1a5]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[110]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 214 bytes, Stack size 20 bytes, stm32f4xx_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[e9]"></a>Receive_OneByte</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, comm.o(i.Receive_OneByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 544 + Unknown Stack Size
<LI>Call Chain = Receive_OneByte &rArr; DataBuf_Decode &rArr; xQueueGenericSendFromISR &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DataBuf_Decode
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[ea]"></a>SEGGER_RTT_ConfigDownBuffer</STRONG> (Thumb, 164 bytes, Stack size 32 bytes, segger_rtt.o(i.SEGGER_RTT_ConfigDownBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SEGGER_RTT_ConfigDownBuffer &rArr; _DoInit &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_DoInit
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
</UL>

<P><STRONG><a name="[ec]"></a>SEGGER_RTT_ConfigUpBuffer</STRONG> (Thumb, 164 bytes, Stack size 32 bytes, segger_rtt.o(i.SEGGER_RTT_ConfigUpBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SEGGER_RTT_ConfigUpBuffer &rArr; _DoInit &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_DoInit
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
</UL>

<P><STRONG><a name="[ed]"></a>SEGGER_RTT_Read</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, segger_rtt.o(i.SEGGER_RTT_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = SEGGER_RTT_Read &rArr; SEGGER_RTT_ReadNoLock &rArr; _DoInit &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_ReadNoLock
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;readFromRTT
</UL>

<P><STRONG><a name="[ee]"></a>SEGGER_RTT_ReadNoLock</STRONG> (Thumb, 148 bytes, Stack size 48 bytes, segger_rtt.o(i.SEGGER_RTT_ReadNoLock))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = SEGGER_RTT_ReadNoLock &rArr; _DoInit &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_DoInit
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Read
</UL>

<P><STRONG><a name="[ef]"></a>SEGGER_RTT_Write</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, segger_rtt.o(i.SEGGER_RTT_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteNoCheck
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_DoInit
</UL>
<BR>[Called By]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
</UL>

<P><STRONG><a name="[f0]"></a>SEGGER_RTT_WriteNoLock</STRONG> (Thumb, 126 bytes, Stack size 32 bytes, segger_rtt.o(i.SEGGER_RTT_WriteNoLock))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SEGGER_RTT_WriteNoLock &rArr; _WriteNoCheck
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteNoCheck
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_WriteBlocking
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_GetAvailWriteSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Write
</UL>

<P><STRONG><a name="[f4]"></a>Send_HeartBeatPacket</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, comm.o(i.Send_HeartBeatPacket))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Send_HeartBeatPacket
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart2_send_char
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::run()
</UL>

<P><STRONG><a name="[e7]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, misc.o(i.SysTick_CLKSourceConfig))
<BR><BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_SysTick_Init
</UL>

<P><STRONG><a name="[1a]"></a>SysTick_Handler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, systemclock.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 440 + Unknown Stack Size
<LI>Call Chain = SysTick_Handler &rArr; xPortSysTickHandler &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>SystemInit</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, system_stm32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(.text)
</UL>
<P><STRONG><a name="[f9]"></a>TIM5_PWM_Init</STRONG> (Thumb, 158 bytes, Stack size 56 bytes, tim.o(i.TIM5_PWM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = TIM5_PWM_Init &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PreloadConfig
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3Init
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::init()
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::SteppingMotor_init(unsigned short, float)
</UL>

<P><STRONG><a name="[100]"></a>TIM_ARRPreloadConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_ARRPreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
</UL>

<P><STRONG><a name="[101]"></a>TIM_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
</UL>

<P><STRONG><a name="[fd]"></a>TIM_CtrlPWMOutputs</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
</UL>

<P><STRONG><a name="[fe]"></a>TIM_OC3Init</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, stm32f4xx_tim.o(i.TIM_OC3Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC3Init
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
</UL>

<P><STRONG><a name="[14f]"></a>TIM_OC3PolarityConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_OC3PolarityConfig))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::init()
</UL>

<P><STRONG><a name="[ff]"></a>TIM_OC3PreloadConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_OC3PreloadConfig))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
</UL>

<P><STRONG><a name="[14b]"></a>TIM_SetCompare3</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_SetCompare3))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::run()
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::SteppingMotor_init(unsigned short, float)
</UL>

<P><STRONG><a name="[fc]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 104 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_TimeBaseInit))
<BR><BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
</UL>

<P><STRONG><a name="[40]"></a>USART1_IRQHandler</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, usart.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART1_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>USART2_IRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, usart.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 552 + Unknown Stack Size
<LI>Call Chain = USART2_IRQHandler &rArr; Receive_OneByte &rArr; DataBuf_Decode &rArr; xQueueGenericSendFromISR &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Receive_OneByte
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[10d]"></a>USART_ClearFlag</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[10e]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_usart.o(i.USART_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[1a6]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[10c]"></a>USART_GetFlagStatus</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_GetFlagStatus))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[10a]"></a>USART_GetITStatus</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[1a7]"></a>USART_ITConfig</STRONG> (Thumb, 74 bytes, Stack size 20 bytes, stm32f4xx_usart.o(i.USART_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[10f]"></a>USART_Init</STRONG> (Thumb, 204 bytes, Stack size 48 bytes, stm32f4xx_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
</UL>

<P><STRONG><a name="[10b]"></a>USART_ReceiveData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[16]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[127]"></a>px4_getpid()</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, uorbdevices.o(i._Z10px4_getpidv))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::close(device::file_t*)
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::open(device::file_t*)
</UL>

<P><STRONG><a name="[75]"></a>task_start(void*)</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, task_start.o(i._Z10task_startPv))
<BR><BR>[Stack]<UL><LI>Max Depth = 664 + Unknown Stack Size
<LI>Call Chain = task_start(void*) &rArr; START::run() &rArr; START::CreateOtherTask() &rArr; Create_task_test &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::run()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> task_start.o(i.Create_task_start)
</UL>
<P><STRONG><a name="[74]"></a>task_Sensor(void*)</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, task_sensor.o(i._Z11task_SensorPv))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = task_Sensor(void*) &rArr; Sensor::run() &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sensor::run()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> task_sensor.o(i.Create_task_sensor)
</UL>
<P><STRONG><a name="[73]"></a>task_control(void*)</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, task_control.o(i._Z12task_controlPv))
<BR><BR>[Stack]<UL><LI>Max Depth = 560 + Unknown Stack Size
<LI>Call Chain = task_control(void*) &rArr; control::run() &rArr; control::command_update() &rArr; xQueueReceive &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::run()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> task_control.o(i.Create_task_control)
</UL>
<P><STRONG><a name="[115]"></a>__strdup(const char*)</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, vdev.o(i._Z8__strdupPKc))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = __strdup(const char*) &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcpy
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::register_driver(const char*, void*)
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
</UL>

<P><STRONG><a name="[11d]"></a>get_vdev(int)</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, uorbmanager.o(i._Z8get_vdevi))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_ioctl(int, int, unsigned long)
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_close(int)
</UL>

<P><STRONG><a name="[117]"></a>px4_open(const char*, int, ...)</STRONG> (Thumb, 196 bytes, Stack size 48 bytes, uorbmanager.o(i._Z8px4_openPKciz))
<BR><BR>[Stack]<UL><LI>Max Depth = 256 + Unknown Stack Size
<LI>Call Chain = px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VFile::createFile(const char*, int)
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::getDev(const char*)
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strncmp
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int)
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_advertise(const orb_metadata*, int*, int)
</UL>

<P><STRONG><a name="[11c]"></a>px4_close(int)</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, uorbmanager.o(i._Z9px4_closei))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = px4_close(int)
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_vdev(int)
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned)
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_advertise(const orb_metadata*, int*, int)
</UL>

<P><STRONG><a name="[11e]"></a>px4_ioctl(int, int, unsigned long)</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, uorbmanager.o(i._Z9px4_ioctliim))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = px4_ioctl(int, int, unsigned long)
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_vdev(int)
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned)
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_advertise(const orb_metadata*, int*, int)
</UL>

<P><STRONG><a name="[76]"></a>task_test(void*)</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, task_test.o(i._Z9task_testPv))
<BR><BR>[Stack]<UL><LI>Max Depth = 504 + Unknown Stack Size
<LI>Call Chain = task_test(void*) &rArr; test::run() &rArr; orb_advertise &rArr; uORB::Manager::orb_advertise(const orb_metadata*, const void*, unsigned) &rArr; uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned) &rArr; uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int) &rArr; uORB::Manager::node_advertise(const orb_metadata*, int*, int) &rArr; px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test::run()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> task_test.o(i.Create_task_test)
</UL>
<P><STRONG><a name="[11f]"></a>test::run()</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, task_test.o(i._ZN4test3runEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 504 + Unknown Stack Size
<LI>Call Chain = test::run() &rArr; orb_advertise &rArr; uORB::Manager::orb_advertise(const orb_metadata*, const void*, unsigned) &rArr; uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned) &rArr; uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int) &rArr; uORB::Manager::node_advertise(const orb_metadata*, int*, int) &rArr; px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_subscribe
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test::init()
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_advertise
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_test(void*)
</UL>

<P><STRONG><a name="[120]"></a>test::init()</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, task_test.o(i._ZN4test4initEv))
<BR><BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test::run()
</UL>

<P><STRONG><a name="[12a]"></a>uORB::DeviceNode::filp_to_sd(device::file_t*)</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode10filp_to_sdEPN6device6file_tE))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long)
</UL>

<P><STRONG><a name="[134]"></a>uORB::DeviceNode::is_published()</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode12is_publishedEv))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
</UL>

<P><STRONG><a name="[128]"></a>uORB::DeviceNode::SubscriberData::set_priority(unsigned char)</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode14SubscriberData12set_priorityEh))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::open(device::file_t*)
</UL>

<P><STRONG><a name="[125]"></a>uORB::DeviceNode::SubscriberData::set_update_reported(bool)</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode14SubscriberData19set_update_reportedEb))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::appears_updated(uORB::DeviceNode::SubscriberData*)
</UL>

<P><STRONG><a name="[123]"></a>uORB::DeviceNode::appears_updated(uORB::DeviceNode::SubscriberData*)</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode15appears_updatedEPNS0_14SubscriberDataE))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = uORB::DeviceNode::appears_updated(uORB::DeviceNode::SubscriberData*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::SubscriberData::set_update_reported(bool)
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long)
</UL>

<P><STRONG><a name="[7f]"></a>uORB::DeviceNode::open(device::file_t*)</STRONG> (Thumb, 136 bytes, Stack size 24 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode4openEPN6device6file_tE))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = uORB::DeviceNode::open(device::file_t*) &rArr; operator new(unsigned) &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::open(device::file_t*)
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::SubscriberData::set_priority(unsigned char)
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_getpid()
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator delete (void*)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
</UL>
<P><STRONG><a name="[80]"></a>uORB::DeviceNode::close(device::file_t*)</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode5closeEPN6device6file_tE))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = uORB::DeviceNode::close(device::file_t*)
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::close(device::file_t*)
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_getpid()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
</UL>
<P><STRONG><a name="[82]"></a>uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long)</STRONG> (Thumb, 212 bytes, Stack size 32 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode5ioctlEPN6device6file_tEim))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long) &rArr; uORB::DeviceNode::appears_updated(uORB::DeviceNode::SubscriberData*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::appears_updated(uORB::DeviceNode::SubscriberData*)
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::filp_to_sd(device::file_t*)
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator delete (void*)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
</UL>
<P><STRONG><a name="[81]"></a>uORB::DeviceNode::write(device::file_t*, const char*, unsigned)</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode5writeEPN6device6file_tEPKcj))
<BR><BR>[Stack]<UL><LI>Max Depth = 176 + Unknown Stack Size
<LI>Call Chain = uORB::DeviceNode::write(device::file_t*, const char*, unsigned) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetAbsoluteTime_us
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new[] (unsigned)
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
</UL>
<P><STRONG><a name="[12c]"></a>uORB::DeviceNode::publish(const orb_metadata*, void*, const void*)</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, uorbdevices.o(i._ZN4uORB10DeviceNode7publishEPK12orb_metadataPvPKv))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uORB::DeviceNode::publish(const orb_metadata*, void*, const void*)
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_publish(const orb_metadata*, void*, const void*)
</UL>

<P><STRONG><a name="[12e]"></a>uORB::DeviceNode::DeviceNode(const orb_metadata*, const char*, const char*, int, unsigned)</STRONG> (Thumb, 66 bytes, Stack size 32 bytes, uorbdevices.o(i._ZN4uORB10DeviceNodeC1EPK12orb_metadataPKcS5_ij))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = uORB::DeviceNode::DeviceNode(const orb_metadata*, const char*, const char*, int, unsigned) &rArr; device::VDev::VDev(const char*, const char*)
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::VDev(const char*, const char*)
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
</UL>

<P><STRONG><a name="[1f5]"></a>uORB::DeviceNode::DeviceNode__sub_object(const orb_metadata*, const char*, const char*, int, unsigned)</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, uorbdevices.o(i._ZN4uORB10DeviceNodeC1EPK12orb_metadataPKcS5_ij), UNUSED)

<P><STRONG><a name="[7d]"></a>uORB::DeviceNode::~DeviceNode__deallocating()</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, uorbdevices.o(i._ZN4uORB10DeviceNodeD0Ev))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = uORB::DeviceNode::~DeviceNode__deallocating() &rArr; uORB::DeviceNode::~DeviceNode() &rArr; operator delete[] (void*) &rArr; operator delete (void*) &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::~DeviceNode()
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator delete (void*)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
</UL>
<P><STRONG><a name="[7c]"></a>uORB::DeviceNode::~DeviceNode()</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, uorbdevices.o(i._ZN4uORB10DeviceNodeD1Ev))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = uORB::DeviceNode::~DeviceNode() &rArr; operator delete[] (void*) &rArr; operator delete (void*) &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::~VDev()
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator delete[] (void*)
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::~DeviceNode__deallocating()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
</UL>
<P><STRONG><a name="[1f6]"></a>uORB::DeviceNode::~DeviceNode__sub_object()</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, uorbdevices.o(i._ZN4uORB10DeviceNodeD1Ev), UNUSED)

<P><STRONG><a name="[131]"></a>uORB::DeviceMaster::getDeviceNodeLocked(const char*)</STRONG> (Thumb, 78 bytes, Stack size 24 bytes, uorbdevices.o(i._ZN4uORB12DeviceMaster19getDeviceNodeLockedEPKc))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uORB::DeviceMaster::getDeviceNodeLocked(const char*)
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
</UL>

<P><STRONG><a name="[87]"></a>uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)</STRONG> (Thumb, 362 bytes, Stack size 144 bytes, uorbdevices.o(i._ZN4uORB12DeviceMaster5ioctlEPN6device6file_tEim))
<BR><BR>[Stack]<UL><LI>Max Depth = 328 + Unknown Stack Size
<LI>Call Chain = uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long) &rArr; uORB::Utils::node_mkpath(char*, uORB::Flavor, const orb_metadata*, int*) &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Utils::node_mkpath(char*, uORB::Flavor, const orb_metadata*, int*)
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strdup(const char*)
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::getDeviceNodeLocked(const char*)
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::DeviceNode(const orb_metadata*, const char*, const char*, int, unsigned)
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::is_published()
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
</UL>
<P><STRONG><a name="[135]"></a>uORB::DeviceMaster::DeviceMaster(uORB::Flavor)</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, uorbdevices.o(i._ZN4uORB12DeviceMasterC1ENS_6FlavorE))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = uORB::DeviceMaster::DeviceMaster(uORB::Flavor) &rArr; device::VDev::VDev(const char*, const char*)
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::VDev(const char*, const char*)
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::get_device_master(uORB::Flavor)
</UL>

<P><STRONG><a name="[1f7]"></a>uORB::DeviceMaster::DeviceMaster__sub_object(uORB::Flavor)</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, uorbdevices.o(i._ZN4uORB12DeviceMasterC1ENS_6FlavorE), UNUSED)

<P><STRONG><a name="[84]"></a>uORB::DeviceMaster::~DeviceMaster__deallocating()</STRONG> (Thumb, 18 bytes, Stack size 16 bytes, uorbdevices.o(i._ZN4uORB12DeviceMasterD0Ev))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = uORB::DeviceMaster::~DeviceMaster__deallocating() &rArr; uORB::DeviceMaster::~DeviceMaster() &rArr; uORB::ORBMap::~ORBMap() &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::~DeviceMaster()
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator delete (void*)
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
</UL>
<P><STRONG><a name="[83]"></a>uORB::DeviceMaster::~DeviceMaster()</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, uorbdevices.o(i._ZN4uORB12DeviceMasterD1Ev))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = uORB::DeviceMaster::~DeviceMaster() &rArr; uORB::ORBMap::~ORBMap() &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::~VDev()
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::ORBMap::~ORBMap()
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::~DeviceMaster__deallocating()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
</UL>
<P><STRONG><a name="[1f8]"></a>uORB::DeviceMaster::~DeviceMaster__sub_object()</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, uorbdevices.o(i._ZN4uORB12DeviceMasterD1Ev), UNUSED)

<P><STRONG><a name="[133]"></a>uORB::Utils::node_mkpath(char*, uORB::Flavor, const orb_metadata*, int*)</STRONG> (Thumb, 66 bytes, Stack size 40 bytes, uorbutils.o(i._ZN4uORB5Utils11node_mkpathEPcNS_6FlavorEPK12orb_metadataPi))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = uORB::Utils::node_mkpath(char*, uORB::Flavor, const orb_metadata*, int*) &rArr; __2snprintf &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2snprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int)
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
</UL>

<P><STRONG><a name="[136]"></a>uORB::ORBMap::~ORBMap()</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, uorbdevices.o(i._ZN4uORB6ORBMapD1Ev))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = uORB::ORBMap::~ORBMap() &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::~DeviceMaster()
</UL>

<P><STRONG><a name="[1f9]"></a>uORB::ORBMap::~ORBMap__sub_object()</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, uorbdevices.o(i._ZN4uORB6ORBMapD1Ev), UNUSED)

<P><STRONG><a name="[137]"></a>uORB::Manager::initialize()</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, uorbmanager.o(i._ZN4uORB7Manager10initializeEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = uORB::Manager::initialize() &rArr; operator new(unsigned) &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::Manager()
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_init
</UL>

<P><STRONG><a name="[139]"></a>uORB::Manager::orb_publish(const orb_metadata*, void*, const void*)</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, uorbmanager.o(i._ZN4uORB7Manager11orb_publishEPK12orb_metadataPvPKv))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = uORB::Manager::orb_publish(const orb_metadata*, void*, const void*) &rArr; uORB::DeviceNode::publish(const orb_metadata*, void*, const void*)
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::publish(const orb_metadata*, void*, const void*)
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned)
</UL>

<P><STRONG><a name="[13a]"></a>uORB::Manager::orb_advertise(const orb_metadata*, const void*, unsigned)</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, uorb.o(i._ZN4uORB7Manager13orb_advertiseEPK12orb_metadataPKvj))
<BR><BR>[Stack]<UL><LI>Max Depth = 488 + Unknown Stack Size
<LI>Call Chain = uORB::Manager::orb_advertise(const orb_metadata*, const void*, unsigned) &rArr; uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned) &rArr; uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int) &rArr; uORB::Manager::node_advertise(const orb_metadata*, int*, int) &rArr; px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned)
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_advertise
</UL>

<P><STRONG><a name="[13c]"></a>uORB::Manager::orb_subscribe(const orb_metadata*)</STRONG> (Thumb, 28 bytes, Stack size 24 bytes, uorbmanager.o(i._ZN4uORB7Manager13orb_subscribeEPK12orb_metadata))
<BR><BR>[Stack]<UL><LI>Max Depth = 424 + Unknown Stack Size
<LI>Call Chain = uORB::Manager::orb_subscribe(const orb_metadata*) &rArr; uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int) &rArr; uORB::Manager::node_advertise(const orb_metadata*, int*, int) &rArr; px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int)
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_subscribe
</UL>

<P><STRONG><a name="[13e]"></a>uORB::Manager::node_advertise(const orb_metadata*, int*, int)</STRONG> (Thumb, 92 bytes, Stack size 40 bytes, uorbmanager.o(i._ZN4uORB7Manager14node_advertiseEPK12orb_metadataPii))
<BR><BR>[Stack]<UL><LI>Max Depth = 296 + Unknown Stack Size
<LI>Call Chain = uORB::Manager::node_advertise(const orb_metadata*, int*, int) &rArr; px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_ioctl(int, int, unsigned long)
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_close(int)
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_open(const char*, int, ...)
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int)
</UL>

<P><STRONG><a name="[13f]"></a>uORB::Manager::get_device_master(uORB::Flavor)</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, uorbmanager.o(i._ZN4uORB7Manager17get_device_masterENS_6FlavorE))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = uORB::Manager::get_device_master(uORB::Flavor) &rArr; operator new(unsigned) &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::DeviceMaster(uORB::Flavor)
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_init
</UL>

<P><STRONG><a name="[13b]"></a>uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned)</STRONG> (Thumb, 118 bytes, Stack size 56 bytes, uorbmanager.o(i._ZN4uORB7Manager19orb_advertise_multiEPK12orb_metadataPKvPiij))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned) &rArr; uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int) &rArr; uORB::Manager::node_advertise(const orb_metadata*, int*, int) &rArr; px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_publish(const orb_metadata*, void*, const void*)
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int)
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_ioctl(int, int, unsigned long)
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_close(int)
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_advertise(const orb_metadata*, const void*, unsigned)
</UL>

<P><STRONG><a name="[13d]"></a>uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int)</STRONG> (Thumb, 212 bytes, Stack size 104 bytes, uorbmanager.o(i._ZN4uORB7Manager9node_openENS_6FlavorEPK12orb_metadataPKvbPii))
<BR><BR>[Stack]<UL><LI>Max Depth = 400 + Unknown Stack Size
<LI>Call Chain = uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int) &rArr; uORB::Manager::node_advertise(const orb_metadata*, int*, int) &rArr; px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::node_advertise(const orb_metadata*, int*, int)
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_open(const char*, int, ...)
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Utils::node_mkpath(char*, uORB::Flavor, const orb_metadata*, int*)
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned)
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_subscribe(const orb_metadata*)
</UL>

<P><STRONG><a name="[138]"></a>uORB::Manager::Manager()</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, uorbmanager.o(i._ZN4uORB7ManagerC1Ev))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::initialize()
</UL>

<P><STRONG><a name="[1fa]"></a>uORB::Manager::Manager__sub_object()</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, uorbmanager.o(i._ZN4uORB7ManagerC1Ev), UNUSED)

<P><STRONG><a name="[140]"></a>START::CreateOtherTask()</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, task_start.o(i._ZN5START15CreateOtherTaskEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 664 + Unknown Stack Size
<LI>Call Chain = START::CreateOtherTask() &rArr; Create_task_test &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_test
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_sensor
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_control
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::run()
</UL>

<P><STRONG><a name="[112]"></a>START::run()</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, task_start.o(i._ZN5START3runEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 664 + Unknown Stack Size
<LI>Call Chain = START::run() &rArr; START::CreateOtherTask() &rArr; Create_task_test &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_advertise
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::init()
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::CreateOtherTask()
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_start(void*)
</UL>

<P><STRONG><a name="[141]"></a>START::init()</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, task_start.o(i._ZN5START4initEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = START::init() &rArr; orb_init &rArr; xTraceRegisterString &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_init
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::run()
</UL>

<P><STRONG><a name="[113]"></a>Sensor::run()</STRONG> (Thumb, 112 bytes, Stack size 0 bytes, task_sensor.o(i._ZN6Sensor3runEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = Sensor::run() &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sensor::init()
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_Sensor(void*)
</UL>

<P><STRONG><a name="[143]"></a>Sensor::init()</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, task_sensor.o(i._ZN6Sensor4initEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Sensor::init() &rArr; PositionSwitch_init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PositionSwitch_init
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sensor::run()
</UL>

<P><STRONG><a name="[145]"></a>device::VDev::register_driver(const char*, void*)</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, vdev.o(i._ZN6device4VDev15register_driverEPKcPv))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__strdup(const char*)
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VFile::createFile(const char*, int)
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::init()
</UL>

<P><STRONG><a name="[7e]"></a>device::VDev::init()</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, vdev.o(i._ZN6device4VDev4initEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + Unknown Stack Size
<LI>Call Chain = device::VDev::init() &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::Device::init()
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::register_driver(const char*, void*)
</UL>
<BR>[Address Reference Count : 4]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB10DeviceNodeE)
<LI> uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
<LI> vdev.o(.constdata__ZTVN6device4VDevE)
<LI> vdev.o(.constdata__ZTVN6device5VFileE)
</UL>
<P><STRONG><a name="[85]"></a>device::VDev::open(device::file_t*)</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, vdev.o(i._ZN6device4VDev4openEPNS_6file_tE))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::open(device::file_t*)
</UL>
<BR>[Address Reference Count : 3]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
<LI> vdev.o(.constdata__ZTVN6device4VDevE)
<LI> vdev.o(.constdata__ZTVN6device5VFileE)
</UL>
<P><STRONG><a name="[86]"></a>device::VDev::close(device::file_t*)</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, vdev.o(i._ZN6device4VDev5closeEPNS_6file_tE))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::close(device::file_t*)
</UL>
<BR>[Address Reference Count : 3]<UL><LI> uorbdevices.o(.constdata__ZTVN4uORB12DeviceMasterE)
<LI> vdev.o(.constdata__ZTVN6device4VDevE)
<LI> vdev.o(.constdata__ZTVN6device5VFileE)
</UL>
<P><STRONG><a name="[89]"></a>device::VDev::ioctl(device::file_t*, int, unsigned long)</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, vdev.o(i._ZN6device4VDev5ioctlEPNS_6file_tEim))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = device::VDev::ioctl(device::file_t*, int, unsigned long)
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long)
</UL>
<BR>[Address Reference Count : 2]<UL><LI> vdev.o(.constdata__ZTVN6device4VDevE)
<LI> vdev.o(.constdata__ZTVN6device5VFileE)
</UL>
<P><STRONG><a name="[118]"></a>device::VDev::getDev(const char*)</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, vdev.o(i._ZN6device4VDev6getDevEPKc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = device::VDev::getDev(const char*)
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_open(const char*, int, ...)
</UL>

<P><STRONG><a name="[12f]"></a>device::VDev::VDev(const char*, const char*)</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, vdev.o(i._ZN6device4VDevC1EPKcS2_))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = device::VDev::VDev(const char*, const char*)
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::Device::Device(const char*)
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VFile::VFile(const char*, int)
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::DeviceMaster(uORB::Flavor)
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::DeviceNode(const orb_metadata*, const char*, const char*, int, unsigned)
</UL>

<P><STRONG><a name="[1fb]"></a>device::VDev::VDev__sub_object(const char*, const char*)</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, vdev.o(i._ZN6device4VDevC1EPKcS2_), UNUSED)

<P><STRONG><a name="[88]"></a>device::VDev::~VDev()</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, vdev.o(i._ZN6device4VDevD1Ev))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = device::VDev::~VDev()
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::Device::~Device()
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::~DeviceMaster()
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::~DeviceNode()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> vdev.o(.constdata__ZTVN6device4VDevE)
</UL>
<P><STRONG><a name="[1fc]"></a>device::VDev::~VDev__sub_object()</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, vdev.o(i._ZN6device4VDevD1Ev), UNUSED)

<P><STRONG><a name="[11a]"></a>device::VFile::createFile(const char*, int)</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, vdev.o(i._ZN6device5VFile10createFileEPKci))
<BR><BR>[Stack]<UL><LI>Max Depth = 208 + Unknown Stack Size
<LI>Call Chain = device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VFile::VFile(const char*, int)
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::register_driver(const char*, void*)
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_open(const char*, int, ...)
</UL>

<P><STRONG><a name="[147]"></a>device::VFile::VFile(const char*, int)</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, vdev.o(i._ZN6device5VFileC1EPKci))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = device::VFile::VFile(const char*, int) &rArr; device::VDev::VDev(const char*, const char*)
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::VDev(const char*, const char*)
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VFile::createFile(const char*, int)
</UL>

<P><STRONG><a name="[1fd]"></a>device::VFile::VFile__sub_object(const char*, int)</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, vdev.o(i._ZN6device5VFileC1EPKci), UNUSED)

<P><STRONG><a name="[8b]"></a>device::Device::init()</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, vdev.o(i._ZN6device6Device4initEv))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::init()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> vdev.o(.constdata__ZTVN6device6DeviceE)
</UL>
<P><STRONG><a name="[146]"></a>device::Device::Device(const char*)</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, vdev.o(i._ZN6device6DeviceC1EPKc))
<BR><BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::VDev(const char*, const char*)
</UL>

<P><STRONG><a name="[1fe]"></a>device::Device::Device__sub_object(const char*)</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, vdev.o(i._ZN6device6DeviceC1EPKc), UNUSED)

<P><STRONG><a name="[8a]"></a>device::Device::~Device()</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, vdev.o(i._ZN6device6DeviceD1Ev))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::~VDev()
</UL>
<BR>[Address Reference Count : 1]<UL><LI> vdev.o(.constdata__ZTVN6device6DeviceE)
</UL>
<P><STRONG><a name="[1ff]"></a>device::Device::~Device__sub_object()</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, vdev.o(i._ZN6device6DeviceD1Ev), UNUSED)

<P><STRONG><a name="[148]"></a>control::command_update()</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, task_control.o(i._ZN7control14command_updateEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 560 + Unknown Stack Size
<LI>Call Chain = control::command_update() &rArr; xQueueReceive &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::run()
</UL>

<P><STRONG><a name="[14a]"></a>control::SteppingMotor_init(unsigned short, float)</STRONG> (Thumb, 590 bytes, Stack size 32 bytes, task_control.o(i._ZN7control18SteppingMotor_initEtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = control::SteppingMotor_init(unsigned short, float) &rArr; TIM5_PWM_Init &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare3
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::run()
</UL>

<P><STRONG><a name="[114]"></a>control::run()</STRONG> (Thumb, 326 bytes, Stack size 0 bytes, task_control.o(i._ZN7control3runEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 560 + Unknown Stack Size
<LI>Call Chain = control::run() &rArr; control::command_update() &rArr; xQueueReceive &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare3
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_HeartBeatPacket
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::Motor_DIR(unsigned char)
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::Motor_EN(unsigned char)
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::init()
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::SteppingMotor_init(unsigned short, float)
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::command_update()
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;task_control(void*)
</UL>

<P><STRONG><a name="[14c]"></a>control::init()</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, task_control.o(i._ZN7control4initEv))
<BR><BR>[Stack]<UL><LI>Max Depth = 520 + Unknown Stack Size
<LI>Call Chain = control::init() &rArr; xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PolarityConfig
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_PWM_Init
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIOA_init
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::run()
</UL>

<P><STRONG><a name="[14d]"></a>control::Motor_EN(unsigned char)</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, task_control.o(i._ZN7control8Motor_ENEh))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::run()
</UL>

<P><STRONG><a name="[14e]"></a>control::Motor_DIR(unsigned char)</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, task_control.o(i._ZN7control9Motor_DIREh))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::run()
</UL>

<P><STRONG><a name="[130]"></a>operator delete[] (void*)</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, array_del.o(i._ZdaPv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = operator delete[] (void*) &rArr; operator delete (void*) &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator delete (void*)
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::~DeviceNode()
</UL>

<P><STRONG><a name="[129]"></a>operator delete (void*)</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, delete.o(i._ZdlPv))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = operator delete (void*) &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::~DeviceMaster__deallocating()
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::~DeviceNode__deallocating()
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::open(device::file_t*)
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator delete[] (void*)
</UL>

<P><STRONG><a name="[12b]"></a>operator new[] (unsigned)</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, array_new.o(i._Znaj))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = operator new[] (unsigned) &rArr; operator new(unsigned) &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new(unsigned)
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::write(device::file_t*, const char*, unsigned)
</UL>

<P><STRONG><a name="[11b]"></a>operator new(unsigned)</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, new.o(i._Znwj))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = operator new(unsigned) &rArr; malloc &rArr; __Heap_Full &rArr; __Heap_ProvideMemory &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_new_handler_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::get_device_master(uORB::Flavor)
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::initialize()
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::register_driver(const char*, void*)
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VFile::createFile(const char*, int)
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;px4_open(const char*, int, ...)
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceMaster::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::open(device::file_t*)
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;operator new[] (unsigned)
</UL>

<P><STRONG><a name="[b3]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[a6]"></a>_sys_exit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usart.o(i._sys_exit))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__sig_exit
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
</UL>

<P><STRONG><a name="[d1]"></a>_ttywrch</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, usart.o(i._ttywrch))
<BR><BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__default_signal_display
</UL>

<P><STRONG><a name="[71]"></a>fputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, usart.o(i.fputc))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_file.o(.text)
</UL>
<P><STRONG><a name="[a1]"></a>main</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 696 + Unknown Stack Size
<LI>Call Chain = main &rArr; Create_task_start &rArr; vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_start
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart2_init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart1_init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_SysTick_Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority(IRQn, unsigned)
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[122]"></a>orb_advertise</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, uorb.o(i.orb_advertise))
<BR><BR>[Stack]<UL><LI>Max Depth = 504 + Unknown Stack Size
<LI>Call Chain = orb_advertise &rArr; uORB::Manager::orb_advertise(const orb_metadata*, const void*, unsigned) &rArr; uORB::Manager::orb_advertise_multi(const orb_metadata*, const void*, int*, int, unsigned) &rArr; uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int) &rArr; uORB::Manager::node_advertise(const orb_metadata*, int*, int) &rArr; px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_advertise(const orb_metadata*, const void*, unsigned)
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test::run()
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::run()
</UL>

<P><STRONG><a name="[142]"></a>orb_init</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, uorb.o(i.orb_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 576<LI>Call Chain = orb_init &rArr; xTraceRegisterString &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTraceRegisterString
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::get_device_master(uORB::Flavor)
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::initialize()
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::init()
</UL>

<P><STRONG><a name="[121]"></a>orb_subscribe</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, uorb.o(i.orb_subscribe))
<BR><BR>[Stack]<UL><LI>Max Depth = 440 + Unknown Stack Size
<LI>Call Chain = orb_subscribe &rArr; uORB::Manager::orb_subscribe(const orb_metadata*) &rArr; uORB::Manager::node_open(uORB::Flavor, const orb_metadata*, const void*, bool, int*, int) &rArr; uORB::Manager::node_advertise(const orb_metadata*, int*, int) &rArr; px4_open(const char*, int, ...) &rArr; device::VFile::createFile(const char*, int) &rArr; device::VDev::register_driver(const char*, void*) &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::Manager::orb_subscribe(const orb_metadata*)
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test::run()
</UL>

<P><STRONG><a name="[15e]"></a>prvAddTaskToStackMonitor</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, trckernelport.o(i.prvAddTaskToStackMonitor))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvAddTaskToStackMonitor
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[1af]"></a>prvIsNewTCB</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, trckernelport.o(i.prvIsNewTCB))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[104]"></a>prvIsValidCommand</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, trcstreamingrecorder.o(i.prvIsValidCommand))
<BR><BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TzCtrl
</UL>

<P><STRONG><a name="[105]"></a>prvProcessCommand</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, trcstreamingrecorder.o(i.prvProcessCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = prvProcessCommand &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TzCtrl
</UL>

<P><STRONG><a name="[108]"></a>prvReportStackUsage</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, trckernelport.o(i.prvReportStackUsage))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = prvReportStackUsage &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxTaskGetStackHighWaterMark
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TzCtrl
</UL>

<P><STRONG><a name="[191]"></a>prvTraceError</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, trcstreamingrecorder.o(i.prvTraceError))
<BR><BR>[Stack]<UL><LI>Max Depth = 264 + In Cycle
<LI>Call Chain = prvTraceError &rArr;  prvSetRecorderEnabled (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSimpleStringEventHelper
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetError
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTraceRegisterString
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent3
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceInitCortexM
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreTSConfig
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSymbolTable
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStartEvent
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreObjectDataTable
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreHeader
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreExtensionInfo
</UL>

<P><STRONG><a name="[163]"></a>prvTraceGetCurrentTaskHandle</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, trckernelport.o(i.prvTraceGetCurrentTaskHandle))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvTraceGetCurrentTaskHandle
</UL>
<BR>[Calls]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetCurrentTaskHandle
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStartEvent
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[195]"></a>prvTraceGetQueueNumber</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, trckernelport.o(i.prvTraceGetQueueNumber))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvTraceGetQueueNumber
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxQueueGetQueueNumber
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSetQueueNumberHigh16
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetQueueNumberHigh16
</UL>

<P><STRONG><a name="[174]"></a>prvTraceGetQueueNumberHigh16</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, trckernelport.o(i.prvTraceGetQueueNumberHigh16))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvTraceGetQueueNumberHigh16 &rArr; prvTraceGetQueueNumber
</UL>
<BR>[Calls]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetQueueNumber
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[164]"></a>prvTraceGetTaskNumberHigh16</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, trckernelport.o(i.prvTraceGetTaskNumberHigh16))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvTraceGetTaskNumberHigh16
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxTaskGetTaskNumber
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[198]"></a>prvTraceInitCortexM</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, trcstreamingrecorder.o(i.prvTraceInitCortexM))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = prvTraceInitCortexM &rArr; prvTraceError &rArr;  prvSetRecorderEnabled (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
</UL>

<P><STRONG><a name="[160]"></a>prvTraceSaveObjectData</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, trcstreamingrecorder.o(i.prvTraceSaveObjectData))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvTraceSaveObjectData
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[15f]"></a>prvTraceSaveObjectSymbol</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, trcstreamingrecorder.o(i.prvTraceSaveObjectSymbol))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prvTraceSaveObjectSymbol
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveSymbol
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
</UL>

<P><STRONG><a name="[199]"></a>prvTraceSaveSymbol</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, trcstreamingrecorder.o(i.prvTraceSaveSymbol))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = prvTraceSaveSymbol &rArr; prvTraceSaveObjectSymbol
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveObjectSymbol
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTraceRegisterString
</UL>

<P><STRONG><a name="[173]"></a>prvTraceSetQueueNumberHigh16</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, trckernelport.o(i.prvTraceSetQueueNumberHigh16))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prvTraceSetQueueNumberHigh16 &rArr; prvTraceGetQueueNumber
</UL>
<BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueSetQueueNumber
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetQueueNumber
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>

<P><STRONG><a name="[162]"></a>prvTraceSetTaskNumberHigh16</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, trckernelport.o(i.prvTraceSetTaskNumberHigh16))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvTraceSetTaskNumberHigh16
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSetTaskNumber
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxTaskGetTaskNumber
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[166]"></a>prvTraceStoreEvent1</STRONG> (Thumb, 110 bytes, Stack size 40 bytes, trcstreamingrecorder.o(i.prvTraceStoreEvent1))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceStop
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetTimestamp32
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[165]"></a>prvTraceStoreEvent2</STRONG> (Thumb, 118 bytes, Stack size 48 bytes, trcstreamingrecorder.o(i.prvTraceStoreEvent2))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceStop
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetTimestamp32
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReportStackUsage
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[19e]"></a>prvTraceStoreEvent3</STRONG> (Thumb, 124 bytes, Stack size 56 bytes, trcstreamingrecorder.o(i.prvTraceStoreEvent3))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = prvTraceStoreEvent3 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceStop
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetTimestamp32
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[193]"></a>prvTraceStoreSimpleStringEventHelper</STRONG> (Thumb, 272 bytes, Stack size 136 bytes, trcstreamingrecorder.o(i.prvTraceStoreSimpleStringEventHelper))
<BR><BR>[Stack]<UL><LI>Max Depth = 248 + In Cycle
<LI>Call Chain = prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceStop
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetTimestamp32
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
</UL>

<P><STRONG><a name="[161]"></a>prvTraceStoreStringEvent</STRONG> (Thumb, 58 bytes, Stack size 48 bytes, trcstreamingrecorder.o(i.prvTraceStoreStringEvent))
<BR><BR>[Stack]<UL><LI>Max Depth = 552<LI>Call Chain = prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEventHelper
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTraceRegisterString
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
</UL>

<P><STRONG><a name="[16a]"></a>prvTraceWarning</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, trcstreamingrecorder.o(i.prvTraceWarning))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSimpleStringEventHelper
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetError
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckRecorderStatus
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreTSConfig
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSymbolTable
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStartEvent
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreObjectDataTable
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreHeader
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreExtensionInfo
</UL>

<P><STRONG><a name="[1a2]"></a>pvPortMalloc</STRONG> (Thumb, 362 bytes, Stack size 32 bytes, heap_4.o(i.pvPortMalloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 472 + Unknown Stack Size
<LI>Call Chain = pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvHeapInit
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[177]"></a>pxPortInitialiseStack</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, port.o(i.pxPortInitialiseStack))
<BR><BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[102]"></a>readFromRTT</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, trcstreamingport.o(i.readFromRTT))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = readFromRTT &rArr; SEGGER_RTT_Read &rArr; SEGGER_RTT_ReadNoLock &rArr; _DoInit &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TzCtrl
</UL>

<P><STRONG><a name="[154]"></a>uart1_init</STRONG> (Thumb, 160 bytes, Stack size 40 bytes, usart.o(i.uart1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = uart1_init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[155]"></a>uart2_init</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, usart.o(i.uart2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = uart2_init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f5]"></a>usart2_send_char</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, usart.o(i.usart2_send_char))
<BR><BR>[Called By]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_HeartBeatPacket
</UL>

<P><STRONG><a name="[159]"></a>uxListRemove</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, list.o(i.uxListRemove))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
</UL>

<P><STRONG><a name="[196]"></a>uxQueueGetQueueNumber</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, queue.o(i.uxQueueGetQueueNumber))
<BR><BR>[Called By]<UL><LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetQueueNumber
</UL>

<P><STRONG><a name="[186]"></a>uxTaskGetStackHighWaterMark</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, tasks.o(i.uxTaskGetStackHighWaterMark))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uxTaskGetStackHighWaterMark
</UL>
<BR>[Calls]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTaskCheckFreeStackSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReportStackUsage
</UL>

<P><STRONG><a name="[197]"></a>uxTaskGetTaskNumber</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, tasks.o(i.uxTaskGetTaskNumber))
<BR><BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSetTaskNumberHigh16
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
</UL>

<P><STRONG><a name="[168]"></a>vListInitialise</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, list.o(i.vListInitialise))
<BR><BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
</UL>

<P><STRONG><a name="[176]"></a>vListInitialiseItem</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, list.o(i.vListInitialiseItem))
<BR><BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
</UL>

<P><STRONG><a name="[15b]"></a>vListInsert</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, list.o(i.vListInsert))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
</UL>

<P><STRONG><a name="[15a]"></a>vListInsertEnd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, list.o(i.vListInsertEnd))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
</UL>

<P><STRONG><a name="[124]"></a>vPortEnterCritical</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, port.o(i.vPortEnterCritical))
<BR><BR>[Stack]<UL><LI>Max Depth = 152 + Unknown Stack Size
<LI>Call Chain = vPortEnterCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::CreateOtherTask()
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::register_driver(const char*, void*)
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::write(device::file_t*, const char*, unsigned)
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::appears_updated(uORB::DeviceNode::SubscriberData*)
</UL>

<P><STRONG><a name="[126]"></a>vPortExitCritical</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, port.o(i.vPortExitCritical))
<BR><BR>[Stack]<UL><LI>Max Depth = 152 + Unknown Stack Size
<LI>Call Chain = vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::CreateOtherTask()
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;device::VDev::register_driver(const char*, void*)
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::write(device::file_t*, const char*, unsigned)
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::ioctl(device::file_t*, int, unsigned long)
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uORB::DeviceNode::appears_updated(uORB::DeviceNode::SubscriberData*)
</UL>

<P><STRONG><a name="[170]"></a>vPortFree</STRONG> (Thumb, 176 bytes, Stack size 24 bytes, heap_4.o(i.vPortFree))
<BR><BR>[Stack]<UL><LI>Max Depth = 464 + Unknown Stack Size
<LI>Call Chain = vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertBlockIntoFreeList
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
</UL>

<P><STRONG><a name="[1b1]"></a>vPortSetupTimerInterrupt</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, port.o(i.vPortSetupTimerInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
</UL>

<P><STRONG><a name="[1aa]"></a>vPortValidateInterruptPriority</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, port.o(i.vPortValidateInterruptPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = vPortValidateInterruptPriority &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortGetIPSR
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
</UL>

<P><STRONG><a name="[169]"></a>vQueueAddToRegistry</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, queue.o(i.vQueueAddToRegistry))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = vQueueAddToRegistry &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEvent
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveObjectSymbol
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
</UL>

<P><STRONG><a name="[19a]"></a>vQueueSetQueueNumber</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, queue.o(i.vQueueSetQueueNumber))
<BR><BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSetQueueNumberHigh16
</UL>

<P><STRONG><a name="[185]"></a>vQueueWaitForMessageRestricted</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, queue.o(i.vQueueWaitForMessageRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 520 + Unknown Stack Size
<LI>Call Chain = vQueueWaitForMessageRestricted &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
</UL>
<BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[109]"></a>vTaskDelay</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, tasks.o(i.vTaskDelay))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TzCtrl
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sensor::run()
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::run()
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test::run()
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;START::run()
</UL>

<P><STRONG><a name="[1b6]"></a>vTaskInternalSetTimeOutState</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, tasks.o(i.vTaskInternalSetTimeOutState))
<BR><BR>[Called By]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[1a1]"></a>vTaskMissedYield</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskMissedYield))
<BR><BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
</UL>

<P><STRONG><a name="[1ad]"></a>vTaskPlaceOnEventList</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, tasks.o(i.vTaskPlaceOnEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = vTaskPlaceOnEventList &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[1ac]"></a>vTaskPlaceOnEventListRestricted</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, tasks.o(i.vTaskPlaceOnEventListRestricted))
<BR><BR>[Stack]<UL><LI>Max Depth = 424 + Unknown Stack Size
<LI>Call Chain = vTaskPlaceOnEventListRestricted &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddCurrentTaskToDelayedList
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[19b]"></a>vTaskSetTaskNumber</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tasks.o(i.vTaskSetTaskNumber))
<BR><BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSetTaskNumberHigh16
</UL>

<P><STRONG><a name="[d7]"></a>vTaskStartScheduler</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, tasks.o(i.vTaskStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 680 + Unknown Stack Size
<LI>Call Chain = vTaskStartScheduler &rArr; xTimerCreateTimerTask &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsNewTCB
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortStartScheduler
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_start
</UL>

<P><STRONG><a name="[183]"></a>vTaskSuspendAll</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, tasks.o(i.vTaskSuspendAll))
<BR><BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[a7]"></a>vTaskSwitchContext</STRONG> (Thumb, 164 bytes, Stack size 8 bytes, tasks.o(i.vTaskSwitchContext))
<BR><BR>[Stack]<UL><LI>Max Depth = 416 + Unknown Stack Size
<LI>Call Chain = vTaskSwitchContext &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsNewTCB
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>

<P><STRONG><a name="[156]"></a>vTraceEnable</STRONG> (Thumb, 210 bytes, Stack size 32 bytes, trckernelport.o(i.vTraceEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 672 + Unknown Stack Size
<LI>Call Chain = vTraceEnable &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;readFromRTT
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_ConfigUpBuffer
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_ConfigDownBuffer
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTraceRegisterString
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceInitCortexM
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessCommand
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsValidCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[103]"></a>vTraceStop</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, trcstreamingrecorder.o(i.vTraceStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TzCtrl
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSimpleStringEventHelper
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent3
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEventHelper
</UL>

<P><STRONG><a name="[19d]"></a>writeToRTT</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, trcstreamingport.o(i.writeToRTT))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = writeToRTT &rArr; SEGGER_RTT_Write &rArr; SEGGER_RTT_WriteNoLock &rArr; _WriteNoCheck
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSimpleStringEventHelper
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent3
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreTSConfig
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSymbolTable
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEventHelper
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStartEvent
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreObjectDataTable
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreHeader
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreExtensionInfo
</UL>

<P><STRONG><a name="[1b0]"></a>xPortStartScheduler</STRONG> (Thumb, 322 bytes, Stack size 16 bytes, port.o(i.xPortStartScheduler))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = xPortStartScheduler &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSetupTimerInterrupt
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvEnableVFP
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__asm___6_port_c_39a90d8d__prvStartFirstTask
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[f7]"></a>xPortSysTickHandler</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, port.o(i.xPortSysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 432 + Unknown Stack Size
<LI>Call Chain = xPortSysTickHandler &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[150]"></a>xQueueGenericCreate</STRONG> (Thumb, 222 bytes, Stack size 40 bytes, queue.o(i.xQueueGenericCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 512 + Unknown Stack Size
<LI>Call Chain = xQueueGenericCreate &rArr; pvPortMalloc &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::init()
</UL>

<P><STRONG><a name="[172]"></a>xQueueGenericReset</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, queue.o(i.xQueueGenericReset))
<BR><BR>[Stack]<UL><LI>Max Depth = 432 + Unknown Stack Size
<LI>Call Chain = xQueueGenericReset &rArr; xTaskRemoveFromEventList &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewQueue
</UL>

<P><STRONG><a name="[1b5]"></a>xQueueGenericSend</STRONG> (Thumb, 832 bytes, Stack size 56 bytes, queue.o(i.xQueueGenericSend))
<BR><BR>[Stack]<UL><LI>Max Depth = 552 + Unknown Stack Size
<LI>Call Chain = xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueFull
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetQueueNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
</UL>

<P><STRONG><a name="[db]"></a>xQueueGenericSendFromISR</STRONG> (Thumb, 406 bytes, Stack size 40 bytes, queue.o(i.xQueueGenericSendFromISR))
<BR><BR>[Stack]<UL><LI>Max Depth = 520 + Unknown Stack Size
<LI>Call Chain = xQueueGenericSendFromISR &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetQueueNumberHigh16
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortValidateInterruptPriority
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DataBuf_Decode
</UL>

<P><STRONG><a name="[149]"></a>xQueueReceive</STRONG> (Thumb, 726 bytes, Stack size 48 bytes, queue.o(i.xQueueReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 544 + Unknown Stack Size
<LI>Call Chain = xQueueReceive &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsQueueEmpty
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataFromQueue
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetQueueNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent3
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCheckForTimeOut
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control::command_update()
</UL>

<P><STRONG><a name="[1b7]"></a>xTaskCheckForTimeOut</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, tasks.o(i.xTaskCheckForTimeOut))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = xTaskCheckForTimeOut &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskInternalSetTimeOutState
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[d4]"></a>xTaskCreate</STRONG> (Thumb, 96 bytes, Stack size 72 bytes, tasks.o(i.xTaskCreate))
<BR><BR>[Stack]<UL><LI>Max Depth = 640 + Unknown Stack Size
<LI>Call Chain = xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseNewTask
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_test
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_sensor
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_control
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Create_task_start
</UL>

<P><STRONG><a name="[194]"></a>xTaskGetCurrentTaskHandle</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetCurrentTaskHandle))
<BR><BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
</UL>

<P><STRONG><a name="[f6]"></a>xTaskGetSchedulerState</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetSchedulerState))
<BR><BR>[Called By]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[dd]"></a>xTaskGetTickCount</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tasks.o(i.xTaskGetTickCount))
<BR><BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FreeRTOS_GetTick
</UL>

<P><STRONG><a name="[1b4]"></a>xTaskIncrementTick</STRONG> (Thumb, 338 bytes, Stack size 24 bytes, tasks.o(i.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 424 + Unknown Stack Size
<LI>Call Chain = xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>

<P><STRONG><a name="[16f]"></a>xTaskPriorityDisinherit</STRONG> (Thumb, 224 bytes, Stack size 24 bytes, tasks.o(i.xTaskPriorityDisinherit))
<BR><BR>[Stack]<UL><LI>Max Depth = 432 + Unknown Stack Size
<LI>Call Chain = xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
</UL>

<P><STRONG><a name="[17c]"></a>xTaskRemoveFromEventList</STRONG> (Thumb, 140 bytes, Stack size 16 bytes, tasks.o(i.xTaskRemoveFromEventList))
<BR><BR>[Stack]<UL><LI>Max Depth = 416 + Unknown Stack Size
<LI>Call Chain = xTaskRemoveFromEventList &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
</UL>

<P><STRONG><a name="[184]"></a>xTaskResumeAll</STRONG> (Thumb, 236 bytes, Stack size 16 bytes, tasks.o(i.xTaskResumeAll))
<BR><BR>[Stack]<UL><LI>Max Depth = 440 + Unknown Stack Size
<LI>Call Chain = xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[1ae]"></a>xTimerCreateTimerTask</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, timers.o(i.xTimerCreateTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 656 + Unknown Stack Size
<LI>Call Chain = xTimerCreateTimerTask &rArr; xTaskCreate &rArr; prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckForValidListAndQueue
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskStartScheduler
</UL>

<P><STRONG><a name="[17f]"></a>xTimerGenericCommand</STRONG> (Thumb, 118 bytes, Stack size 48 bytes, timers.o(i.xTimerGenericCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 600 + Unknown Stack Size
<LI>Call Chain = xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>

<P><STRONG><a name="[106]"></a>xTraceIsRecordingEnabled</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, trcstreamingrecorder.o(i.xTraceIsRecordingEnabled))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TzCtrl
</UL>

<P><STRONG><a name="[157]"></a>xTraceRegisterString</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, trcstreamingrecorder.o(i.xTraceRegisterString))
<BR><BR>[Stack]<UL><LI>Max Depth = 568<LI>Call Chain = xTraceRegisterString &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEvent
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveSymbol
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceEnable
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;orb_init
</UL>

<P><STRONG><a name="[e3]"></a>__aeabi_ul2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, ffltll_clz.o(x$fpl$ffltll))
<BR><BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetAbsoluteTime_us
</UL>

<P><STRONG><a name="[200]"></a>_ll_uto_f</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, ffltll_clz.o(x$fpl$ffltll), UNUSED)

<P><STRONG><a name="[201]"></a>__aeabi_l2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, ffltll_clz.o(x$fpl$ffltll), UNUSED)

<P><STRONG><a name="[202]"></a>_ll_sto_f</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, ffltll_clz.o(x$fpl$ffltll), UNUSED)

<P><STRONG><a name="[1ba]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_ufrom_f
</UL>

<P><STRONG><a name="[98]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[203]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[204]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[e4]"></a>__aeabi_f2ulz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffixull.o(x$fpl$llufromf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetAbsoluteTime_us
</UL>

<P><STRONG><a name="[1b9]"></a>_ll_ufrom_f</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, ffixull.o(x$fpl$llufromf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[f8]"></a>SetSysClock</STRONG> (Thumb, 220 bytes, Stack size 12 bytes, system_stm32f4xx.o(i.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[188]"></a>__get_PRIMASK</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, trcstreamingrecorder.o(i.__get_PRIMASK))
<BR><BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSimpleStringEventHelper
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent3
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveSymbol
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveObjectSymbol
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveObjectData
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSymbolTable
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEventHelper
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStartEvent
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreObjectDataTable
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreHeader
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreExtensionInfo
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>

<P><STRONG><a name="[189]"></a>__set_PRIMASK</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, trcstreamingrecorder.o(i.__set_PRIMASK))
<BR><BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSimpleStringEventHelper
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent3
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveSymbol
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveObjectSymbol
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveObjectData
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSymbolTable
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEventHelper
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStartEvent
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreObjectDataTable
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreHeader
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreExtensionInfo
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>

<P><STRONG><a name="[19c]"></a>prvGetTimestamp32</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, trcstreamingrecorder.o(i.prvGetTimestamp32))
<BR><BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSimpleStringEventHelper
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent3
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreTSConfig
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEventHelper
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStartEvent
</UL>

<P><STRONG><a name="[17d]"></a>prvSetRecorderEnabled</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, trcstreamingrecorder.o(i.prvSetRecorderEnabled))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreTSConfig
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreSymbolTable
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStartEvent
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreObjectDataTable
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreHeader
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreExtensionInfo
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceStop
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessCommand
</UL>

<P><STRONG><a name="[192]"></a>prvTraceGetError</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, trcstreamingrecorder.o(i.prvTraceGetError))
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
</UL>

<P><STRONG><a name="[18d]"></a>prvTraceStoreExtensionInfo</STRONG> (Thumb, 100 bytes, Stack size 40 bytes, trcstreamingrecorder.o(i.prvTraceStoreExtensionInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = prvTraceStoreExtensionInfo &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>

<P><STRONG><a name="[18a]"></a>prvTraceStoreHeader</STRONG> (Thumb, 146 bytes, Stack size 56 bytes, trcstreamingrecorder.o(i.prvTraceStoreHeader))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = prvTraceStoreHeader &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>

<P><STRONG><a name="[18c]"></a>prvTraceStoreObjectDataTable</STRONG> (Thumb, 132 bytes, Stack size 48 bytes, trcstreamingrecorder.o(i.prvTraceStoreObjectDataTable))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = prvTraceStoreObjectDataTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>

<P><STRONG><a name="[18e]"></a>prvTraceStoreStartEvent</STRONG> (Thumb, 172 bytes, Stack size 56 bytes, trcstreamingrecorder.o(i.prvTraceStoreStartEvent))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = prvTraceStoreStartEvent &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetTimestamp32
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>

<P><STRONG><a name="[19f]"></a>prvTraceStoreStringEventHelper</STRONG> (Thumb, 264 bytes, Stack size 144 bytes, trcstreamingrecorder.o(i.prvTraceStoreStringEventHelper))
<BR><BR>[Stack]<UL><LI>Max Depth = 504<LI>Call Chain = prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceStop
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetTimestamp32
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEvent
</UL>

<P><STRONG><a name="[18b]"></a>prvTraceStoreSymbolTable</STRONG> (Thumb, 138 bytes, Stack size 72 bytes, trcstreamingrecorder.o(i.prvTraceStoreSymbolTable))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_PRIMASK
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__get_PRIMASK
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>

<P><STRONG><a name="[18f]"></a>prvTraceStoreTSConfig</STRONG> (Thumb, 146 bytes, Stack size 56 bytes, trcstreamingrecorder.o(i.prvTraceStoreTSConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = prvTraceStoreTSConfig &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;writeToRTT
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceError
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetTimestamp32
</UL>
<BR>[Called By]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSetRecorderEnabled
</UL>

<P><STRONG><a name="[7a]"></a>TzCtrl</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, trckernelport.o(i.TzCtrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 472 + Unknown Stack Size
<LI>Call Chain = TzCtrl &rArr; vTaskDelay &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;readFromRTT
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvReportStackUsage
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckRecorderStatus
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTraceIsRecordingEnabled
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTraceStop
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessCommand
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIsValidCommand
</UL>
<BR>[Address Reference Count : 1]<UL><LI> trckernelport.o(i.vTraceEnable)
</UL>
<P><STRONG><a name="[107]"></a>prvCheckRecorderStatus</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, trckernelport.o(i.prvCheckRecorderStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = prvCheckRecorderStatus &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceWarning
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TzCtrl
</UL>

<P><STRONG><a name="[eb]"></a>_DoInit</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, segger_rtt.o(i._DoInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = _DoInit &rArr; strcpy
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_ReadNoLock
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_Write
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_ConfigUpBuffer
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_ConfigDownBuffer
</UL>

<P><STRONG><a name="[f1]"></a>_GetAvailWriteSpace</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, segger_rtt.o(i._GetAvailWriteSpace))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _GetAvailWriteSpace
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[f3]"></a>_WriteBlocking</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, segger_rtt.o(i._WriteBlocking))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _WriteBlocking
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[f2]"></a>_WriteNoCheck</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, segger_rtt.o(i._WriteNoCheck))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _WriteNoCheck
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SEGGER_RTT_WriteNoLock
</UL>

<P><STRONG><a name="[16d]"></a>prvCopyDataFromQueue</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, queue.o(i.prvCopyDataFromQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = prvCopyDataFromQueue
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[16e]"></a>prvCopyDataToQueue</STRONG> (Thumb, 126 bytes, Stack size 24 bytes, queue.o(i.prvCopyDataToQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 456 + Unknown Stack Size
<LI>Call Chain = prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskPriorityDisinherit
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[171]"></a>prvInitialiseNewQueue</STRONG> (Thumb, 152 bytes, Stack size 32 bytes, queue.o(i.prvInitialiseNewQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 464 + Unknown Stack Size
<LI>Call Chain = prvInitialiseNewQueue &rArr; xQueueGenericReset &rArr; xTaskRemoveFromEventList &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSetQueueNumberHigh16
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetQueueNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericReset
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
</UL>

<P><STRONG><a name="[179]"></a>prvIsQueueEmpty</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, queue.o(i.prvIsQueueEmpty))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = prvIsQueueEmpty &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
</UL>

<P><STRONG><a name="[17a]"></a>prvIsQueueFull</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, queue.o(i.prvIsQueueFull))
<BR><BR>[Stack]<UL><LI>Max Depth = 168 + Unknown Stack Size
<LI>Call Chain = prvIsQueueFull &rArr; vPortExitCritical &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
</UL>
<BR>[Called By]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[17b]"></a>prvNotifyQueueSetContainer</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, queue.o(i.prvNotifyQueueSetContainer))
<BR><BR>[Stack]<UL><LI>Max Depth = 480 + Unknown Stack Size
<LI>Call Chain = prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCopyDataToQueue
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetQueueNumberHigh16
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvUnlockQueue
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSendFromISR
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
</UL>

<P><STRONG><a name="[1a0]"></a>prvUnlockQueue</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, queue.o(i.prvUnlockQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 496 + Unknown Stack Size
<LI>Call Chain = prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvNotifyQueueSetContainer
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskRemoveFromEventList
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskMissedYield
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericSend
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>

<P><STRONG><a name="[158]"></a>prvAddCurrentTaskToDelayedList</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, tasks.o(i.prvAddCurrentTaskToDelayedList))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvAddCurrentTaskToDelayedList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskDelay
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventListRestricted
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskPlaceOnEventList
</UL>

<P><STRONG><a name="[15c]"></a>prvAddNewTaskToReadyList</STRONG> (Thumb, 270 bytes, Stack size 16 bytes, tasks.o(i.prvAddNewTaskToReadyList))
<BR><BR>[Stack]<UL><LI>Max Depth = 568 + Unknown Stack Size
<LI>Call Chain = prvAddNewTaskToReadyList &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsertEnd
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSetTaskNumberHigh16
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetTaskNumberHigh16
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddTaskToStackMonitor
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceGetCurrentTaskHandle
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreStringEvent
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent2
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceStoreEvent1
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveObjectSymbol
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTraceSaveObjectData
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInitialiseTaskLists
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[16b]"></a>prvCheckTasksWaitingTermination</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, tasks.o(i.prvCheckTasksWaitingTermination))
<BR><BR>[Stack]<UL><LI>Max Depth = 480 + Unknown Stack Size
<LI>Call Chain = prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvDeleteTCB
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvIdleTask
</UL>

<P><STRONG><a name="[16c]"></a>prvDeleteTCB</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, tasks.o(i.prvDeleteTCB))
<BR><BR>[Stack]<UL><LI>Max Depth = 472 + Unknown Stack Size
<LI>Call Chain = prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
</UL>

<P><STRONG><a name="[79]"></a>prvIdleTask</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, tasks.o(i.prvIdleTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 480 + Unknown Stack Size
<LI>Call Chain = prvIdleTask &rArr; prvCheckTasksWaitingTermination &rArr; prvDeleteTCB &rArr; vPortFree &rArr; xTaskResumeAll &rArr; xTaskIncrementTick &rArr; prvTraceStoreEvent1 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvCheckTasksWaitingTermination
</UL>
<BR>[Address Reference Count : 1]<UL><LI> tasks.o(i.vTaskStartScheduler)
</UL>
<P><STRONG><a name="[175]"></a>prvInitialiseNewTask</STRONG> (Thumb, 180 bytes, Stack size 40 bytes, tasks.o(i.prvInitialiseNewTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 184 + Unknown Stack Size
<LI>Call Chain = prvInitialiseNewTask &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialiseItem
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pxPortInitialiseStack
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskCreate
</UL>

<P><STRONG><a name="[15d]"></a>prvInitialiseTaskLists</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, tasks.o(i.prvInitialiseTaskLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvInitialiseTaskLists
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvAddNewTaskToReadyList
</UL>

<P><STRONG><a name="[1b8]"></a>prvResetNextTaskUnblockTime</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, tasks.o(i.prvResetNextTaskUnblockTime))
<BR><BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>

<P><STRONG><a name="[1a9]"></a>prvTaskCheckFreeStackSpace</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, tasks.o(i.prvTaskCheckFreeStackSpace))
<BR><BR>[Called By]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxTaskGetStackHighWaterMark
</UL>

<P><STRONG><a name="[167]"></a>prvCheckForValidListAndQueue</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, timers.o(i.prvCheckForValidListAndQueue))
<BR><BR>[Stack]<UL><LI>Max Depth = 576 + Unknown Stack Size
<LI>Call Chain = prvCheckForValidListAndQueue &rArr; vQueueAddToRegistry &rArr; prvTraceStoreStringEvent &rArr; prvTraceStoreStringEventHelper &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortExitCritical
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortEnterCritical
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInitialise
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueGenericCreate
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueAddToRegistry
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerCreateTimerTask
</UL>

<P><STRONG><a name="[190]"></a>prvGetNextExpireTime</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, timers.o(i.prvGetNextExpireTime))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[178]"></a>prvInsertTimerInActiveList</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, timers.o(i.prvInsertTimerInActiveList))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = prvInsertTimerInActiveList &rArr; vListInsert
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
</UL>
<BR>[Called By]<UL><LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
</UL>

<P><STRONG><a name="[17e]"></a>prvProcessExpiredTimer</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, timers.o(i.prvProcessExpiredTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 624 + Unknown Stack Size
<LI>Call Chain = prvProcessExpiredTimer &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
</UL>

<P><STRONG><a name="[180]"></a>prvProcessReceivedCommands</STRONG> (Thumb, 318 bytes, Stack size 48 bytes, timers.o(i.prvProcessReceivedCommands))
<BR><BR>[Stack]<UL><LI>Max Depth = 696 + Unknown Stack Size
<LI>Call Chain = prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvInsertTimerInActiveList
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xQueueReceive
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[182]"></a>prvProcessTimerOrBlockTask</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, timers.o(i.prvProcessTimerOrBlockTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 672 + Unknown Stack Size
<LI>Call Chain = prvProcessTimerOrBlockTask &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskResumeAll
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSuspendAll
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessExpiredTimer
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vQueueWaitForMessageRestricted
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvTimerTask
</UL>

<P><STRONG><a name="[181]"></a>prvSampleTimeNow</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, timers.o(i.prvSampleTimeNow))
<BR><BR>[Stack]<UL><LI>Max Depth = 648 + Unknown Stack Size
<LI>Call Chain = prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSwitchTimerLists
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetTickCount
</UL>
<BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
</UL>

<P><STRONG><a name="[187]"></a>prvSwitchTimerLists</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, timers.o(i.prvSwitchTimerLists))
<BR><BR>[Stack]<UL><LI>Max Depth = 632 + Unknown Stack Size
<LI>Call Chain = prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vListInsert
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uxListRemove
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTimerGenericCommand
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvSampleTimeNow
</UL>

<P><STRONG><a name="[7b]"></a>prvTimerTask</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, timers.o(i.prvTimerTask))
<BR><BR>[Stack]<UL><LI>Max Depth = 704 + Unknown Stack Size
<LI>Call Chain = prvTimerTask &rArr; prvProcessReceivedCommands &rArr; prvSampleTimeNow &rArr; prvSwitchTimerLists &rArr; xTimerGenericCommand &rArr; xQueueGenericSend &rArr; prvUnlockQueue &rArr; prvNotifyQueueSetContainer &rArr; prvCopyDataToQueue &rArr; xTaskPriorityDisinherit &rArr; prvTraceStoreEvent2 &rArr; vTraceStop &rArr; prvSetRecorderEnabled &rArr; prvTraceStoreSymbolTable &rArr; prvTraceWarning &rArr; prvTraceStoreSimpleStringEventHelper &rArr;  vTraceStop (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessTimerOrBlockTask
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvProcessReceivedCommands
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvGetNextExpireTime
</UL>
<BR>[Address Reference Count : 1]<UL><LI> timers.o(i.xTimerCreateTimerTask)
</UL>
<P><STRONG><a name="[1a3]"></a>prvHeapInit</STRONG> (Thumb, 98 bytes, Stack size 12 bytes, heap_4.o(i.prvHeapInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = prvHeapInit
</UL>
<BR>[Called By]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[1a4]"></a>prvInsertBlockIntoFreeList</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, heap_4.o(i.prvInsertBlockIntoFreeList))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prvInsertBlockIntoFreeList
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortFree
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pvPortMalloc
</UL>

<P><STRONG><a name="[78]"></a>prvTaskExitError</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, port.o(i.prvTaskExitError))
<BR><BR>[Stack]<UL><LI>Max Depth = 144 + Unknown Stack Size
<LI>Call Chain = prvTaskExitError &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> port.o(i.pxPortInitialiseStack)
</UL>
<P><STRONG><a name="[8c]"></a>__sti___13_task_test_cpp_27549090</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, task_test.o(i.__sti___13_task_test_cpp_27549090))
<BR>[Address Reference Count : 1]<UL><LI> task_test.o(.init_array)
</UL>
<P><STRONG><a name="[8d]"></a>__sti___16_task_Control_cpp_97739fc6</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, task_control.o(i.__sti___16_task_Control_cpp_97739fc6))
<BR>[Address Reference Count : 1]<UL><LI> task_control.o(.init_array)
</UL>
<P><STRONG><a name="[153]"></a>NVIC_SetPriority(IRQn, unsigned)</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, main.o(i._Z16NVIC_SetPriority4IRQnj))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority(IRQn, unsigned)
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[70]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL>
<P><STRONG><a name="[72]"></a>[local to arm_exceptions_c]::__default_terminate_handler()</STRONG> (Thumb, 6 bytes, Stack size 8 bytes, arm_exceptions_globs.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48 + Unknown Stack Size
<LI>Call Chain = [local to arm_exceptions_c]::__default_terminate_handler() &rArr; abort &rArr; __rt_SIGABRT &rArr; __rt_SIGABRT_inner &rArr; __default_signal_display
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;abort
</UL>
<BR>[Address Reference Count : 1]<UL><LI> arm_exceptions_globs.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
