--cpu=Cortex-M4.fp.sp
".\objects\system_stm32f4xx.o"
".\objects\startup_stm32f40_41xxx.o"
".\objects\misc.o"
".\objects\stm32f4xx_adc.o"
".\objects\stm32f4xx_can.o"
".\objects\stm32f4xx_cec.o"
".\objects\stm32f4xx_crc.o"
".\objects\stm32f4xx_cryp.o"
".\objects\stm32f4xx_cryp_aes.o"
".\objects\stm32f4xx_cryp_des.o"
".\objects\stm32f4xx_cryp_tdes.o"
".\objects\stm32f4xx_dac.o"
".\objects\stm32f4xx_dbgmcu.o"
".\objects\stm32f4xx_dcmi.o"
".\objects\stm32f4xx_dfsdm.o"
".\objects\stm32f4xx_dma.o"
".\objects\stm32f4xx_dma2d.o"
".\objects\stm32f4xx_dsi.o"
".\objects\stm32f4xx_exti.o"
".\objects\stm32f4xx_flash.o"
".\objects\stm32f4xx_flash_ramfunc.o"
".\objects\stm32f4xx_fmpi2c.o"
".\objects\stm32f4xx_fsmc.o"
".\objects\stm32f4xx_gpio.o"
".\objects\stm32f4xx_hash.o"
".\objects\stm32f4xx_hash_md5.o"
".\objects\stm32f4xx_hash_sha1.o"
".\objects\stm32f4xx_i2c.o"
".\objects\stm32f4xx_iwdg.o"
".\objects\stm32f4xx_lptim.o"
".\objects\stm32f4xx_ltdc.o"
".\objects\stm32f4xx_pwr.o"
".\objects\stm32f4xx_qspi.o"
".\objects\stm32f4xx_rcc.o"
".\objects\stm32f4xx_rng.o"
".\objects\stm32f4xx_rtc.o"
".\objects\stm32f4xx_sai.o"
".\objects\stm32f4xx_sdio.o"
".\objects\stm32f4xx_spdifrx.o"
".\objects\stm32f4xx_spi.o"
".\objects\stm32f4xx_syscfg.o"
".\objects\stm32f4xx_tim.o"
".\objects\stm32f4xx_usart.o"
".\objects\stm32f4xx_wwdg.o"
".\objects\trcstreamingrecorder.o"
".\objects\trckernelport.o"
".\objects\trcstreamingport.o"
".\objects\segger_rtt.o"
".\objects\croutine.o"
".\objects\event_groups.o"
".\objects\list.o"
".\objects\queue.o"
".\objects\tasks.o"
".\objects\timers.o"
".\objects\stream_buffer.o"
".\objects\heap_4.o"
".\objects\port.o"
".\objects\task_start.o"
".\objects\task_test.o"
".\objects\task_control.o"
".\objects\task_sensor.o"
".\objects\systemclock.o"
".\objects\usart.o"
".\objects\tim.o"
".\objects\led.o"
".\objects\io.o"
".\objects\uorb.o"
".\objects\uorbdevices.o"
".\objects\uorbmanager.o"
".\objects\uorbutils.o"
".\objects\vdev.o"
".\objects\objects_user_defined.o"
".\objects\stm32f4xx_it.o"
".\objects\main.o"
".\objects\comm.o"
--strict --scatter ".\Objects\Template-FreeRTOS.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\Template-FreeRTOS.map" -o .\Objects\Template-FreeRTOS.axf