.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\portable\RVDS\ARM_CM4F\port.c
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\port.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\port.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\port.o: ..\SourceCode\ApplicationLayer\User\FreeRTOSConfig.h
.\objects\port.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcRecorder.h
.\objects\port.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcConfig.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcPortDefines.h
.\objects\port.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32F4xx.h
.\objects\port.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cm4.h
.\objects\port.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmInstr.h
.\objects\port.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmFunc.h
.\objects\port.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmSimd.h
.\objects\port.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h
.\objects\port.o: ..\SourceCode\ApplicationLayer\User\stm32f4xx_conf.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\objects\port.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\objects\port.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcStreamingConfig.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcHardwarePort.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\trcStreamingPort.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT_Conf.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcKernelPort.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\projdefs.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\portable.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\deprecated_definitions.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\mpu_wrappers.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\task.h
.\objects\port.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\list.h
