.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\queue.c
.\objects\queue.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\queue.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\string.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\queue.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\queue.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\queue.o: ..\SourceCode\ApplicationLayer\User\FreeRTOSConfig.h
.\objects\queue.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcRecorder.h
.\objects\queue.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcConfig.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcPortDefines.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32F4xx.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cm4.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmInstr.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmFunc.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmSimd.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h
.\objects\queue.o: ..\SourceCode\ApplicationLayer\User\stm32f4xx_conf.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\objects\queue.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcStreamingConfig.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcHardwarePort.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\trcStreamingPort.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT_Conf.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcKernelPort.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\projdefs.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\portable.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\deprecated_definitions.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\mpu_wrappers.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\task.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\list.h
.\objects\queue.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\queue.h
