.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\tasks.c
.\objects\tasks.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\tasks.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\string.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\tasks.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\tasks.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\tasks.o: ..\SourceCode\ApplicationLayer\User\FreeRTOSConfig.h
.\objects\tasks.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcRecorder.h
.\objects\tasks.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcConfig.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcPortDefines.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32F4xx.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cm4.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmInstr.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmFunc.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmSimd.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h
.\objects\tasks.o: ..\SourceCode\ApplicationLayer\User\stm32f4xx_conf.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\objects\tasks.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcStreamingConfig.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcHardwarePort.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\trcStreamingPort.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT_Conf.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcKernelPort.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\projdefs.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\portable.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\deprecated_definitions.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\mpu_wrappers.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\task.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\list.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\timers.h
.\objects\tasks.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\stack_macros.h
