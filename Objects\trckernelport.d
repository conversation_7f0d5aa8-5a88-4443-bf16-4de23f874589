.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\trcKernelPort.c
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\trckernelport.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\trckernelport.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\trckernelport.o: ..\SourceCode\ApplicationLayer\User\FreeRTOSConfig.h
.\objects\trckernelport.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcRecorder.h
.\objects\trckernelport.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcConfig.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcPortDefines.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32F4xx.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cm4.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmInstr.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmFunc.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmSimd.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h
.\objects\trckernelport.o: ..\SourceCode\ApplicationLayer\User\stm32f4xx_conf.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\objects\trckernelport.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcStreamingConfig.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcHardwarePort.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\trcStreamingPort.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT_Conf.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcKernelPort.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\projdefs.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\portable.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\deprecated_definitions.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\mpu_wrappers.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\task.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\list.h
.\objects\trckernelport.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\queue.h
