.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\trcStreamingRecorder.c
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcRecorder.h
.\objects\trcstreamingrecorder.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\trcstreamingrecorder.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\trcstreamingrecorder.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcConfig.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcPortDefines.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32F4xx.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cm4.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmInstr.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmFunc.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmSimd.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\ApplicationLayer\User\stm32f4xx_conf.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcStreamingConfig.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcHardwarePort.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\trcStreamingPort.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT_Conf.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcKernelPort.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\ApplicationLayer\User\FreeRTOSConfig.h
.\objects\trcstreamingrecorder.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcRecorder.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\projdefs.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\portable.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\deprecated_definitions.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\mpu_wrappers.h
.\objects\trcstreamingrecorder.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\string.h
.\objects\trcstreamingrecorder.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcExtensions.h
