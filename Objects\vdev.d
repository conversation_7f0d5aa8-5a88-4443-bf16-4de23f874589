.\objects\vdev.o: ..\SourceCode\ApplicationLayer\uORB\vdev.cpp
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\uORB\vdev.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\uORB\errno.h
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\uORB\defines.h
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\uORB\DriverFramework/DevIOCTL.h
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\User\main.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\string.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\ctype.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\math.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cm4.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmInstr.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmFunc.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\CMSIS\Include\core_cmSimd.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\User\stm32f4xx_conf.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_adc.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_crc.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dbgmcu.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dma.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_exti.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_flash.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_gpio.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_i2c.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_iwdg.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_pwr.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rcc.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rtc.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_sdio.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_spi.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_syscfg.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_tim.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_usart.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_wwdg.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\misc.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_cryp.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_hash.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_rng.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_can.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dac.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_dcmi.h
.\objects\vdev.o: ..\SourceCode\HardwareLayer\STM32F4xx_StdPeriph_Driver\inc\stm32f4xx_fsmc.h
.\objects\vdev.o: ..\SourceCode\DriverLayer\SystemClock\SystemClock.h
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\User\main.h
.\objects\vdev.o: ..\SourceCode\DriverLayer\Led\Led.h
.\objects\vdev.o: ..\SourceCode\DriverLayer\Usart\Usart.h
.\objects\vdev.o: ..\SourceCode\DriverLayer\TIM\TIM.h
.\objects\vdev.o: ..\SourceCode\DriverLayer\IO\IO.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Task\task_global.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\User\FreeRTOSConfig.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcRecorder.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcConfig.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcPortDefines.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\config\trcStreamingConfig.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcHardwarePort.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\trcStreamingPort.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT_Conf.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\streamports\Jlink_RTT\include\SEGGER_RTT.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Plus-Trace\Include\trcKernelPort.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\FreeRTOS.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\projdefs.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\portable.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\deprecated_definitions.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\portable\RVDS\ARM_CM4F\portmacro.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\mpu_wrappers.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\task.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\list.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\queue.h
.\objects\vdev.o: ..\SourceCode\OSLayer\FreeRTOS-Kernel\include\semphr.h
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\uORB\uORB.h
.\objects\vdev.o: ..\SourceCode\ApplicationLayer\User\comm.h
.\objects\vdev.o: D:\MDK3.2\ARM\ARMCC\Bin\..\include\errno.h
