# 频率捕获和倍频输出测试指南

## 测试准备

### 硬件要求
1. STM32F407开发板
2. 信号发生器或函数发生器
3. 示波器（用于验证输出）
4. 串口调试工具
5. 连接线

### 软件要求
1. Keil MDK开发环境
2. 串口调试助手
3. 示波器软件（可选）

## 测试步骤

### 1. 编译和下载程序
```bash
1. 打开Keil项目文件 Template-FreeRTOS.uvprojx
2. 编译项目 (F7)
3. 下载程序到开发板
4. 复位开发板
```

### 2. 硬件连接
```
信号发生器输出 → PA3 (输入捕获)
PE3 → 示波器通道1 (观察输出)
串口 → PC (调试信息)
```

### 3. 基本功能测试

#### 测试1：1kHz输入信号
1. 设置信号发生器输出1kHz方波，3.3V幅度
2. 连接到PA3引脚
3. 观察串口输出：
   ```
   Input Frequency: 1000 Hz, Output Frequency: 5000 Hz (5x)
   ```
4. 用示波器测量PE3输出，应为5kHz方波

#### 测试2：不同频率测试
测试以下频率点：
- 100Hz → 500Hz输出
- 500Hz → 2.5kHz输出  
- 2kHz → 10kHz输出
- 5kHz → 25kHz输出
- 10kHz → 50kHz输出

#### 测试3：边界条件测试
1. **无输入信号**：
   - 断开PA3连接
   - 观察串口输出警告信息
   
2. **高频输入**：
   - 输入20kHz信号
   - 检查输出是否被限制在100kHz以下

### 4. 精度测试

使用精确的信号发生器和频率计：
1. 输入1000.0Hz
2. 测量输出频率精度
3. 计算误差百分比

### 5. 稳定性测试

1. **长时间运行**：连续运行1小时，观察频率稳定性
2. **温度测试**：在不同温度下测试频率精度
3. **电源波动**：测试电源电压变化对精度的影响

## 预期结果

### 正常工作状态
- 串口每秒输出一次频率信息
- 输出频率 = 输入频率 × 5
- PWM占空比约50%
- 响应时间 < 100ms

### 异常状态处理
- 无输入信号时显示警告
- 超出频率范围时限制输出
- 系统稳定运行，无死机

## 故障排除

### 问题1：无频率输出
**可能原因**：
- PA3连接不良
- 输入信号电平不匹配
- 定时器配置错误

**解决方法**：
- 检查硬件连接
- 确认输入信号为3.3V TTL电平
- 检查TIM2配置

### 问题2：频率不准确
**可能原因**：
- 系统时钟不准确
- 定时器分频设置错误
- 输入信号质量差

**解决方法**：
- 校准系统时钟
- 检查定时器配置
- 改善输入信号质量

### 问题3：输出不稳定
**可能原因**：
- 输入信号有噪声
- 电源不稳定
- 中断处理延迟

**解决方法**：
- 添加输入滤波
- 稳定电源供应
- 优化中断处理

## 性能指标

### 测量精度
- 频率范围：1Hz - 20kHz
- 测量精度：±0.1%
- 分辨率：1Hz

### 响应特性
- 建立时间：< 2个输入周期
- 稳定时间：< 100ms
- 更新频率：实时

### 输出特性
- 倍频比：5倍
- 占空比：50% ±1%
- 输出电平：3.3V TTL

## 测试记录表

| 输入频率(Hz) | 预期输出(Hz) | 实际输出(Hz) | 误差(%) | 备注 |
|-------------|-------------|-------------|---------|------|
| 100         | 500         |             |         |      |
| 500         | 2500        |             |         |      |
| 1000        | 5000        |             |         |      |
| 2000        | 10000       |             |         |      |
| 5000        | 25000       |             |         |      |
| 10000       | 50000       |             |         |      |

## 注意事项

1. **安全操作**：确保信号电平在安全范围内
2. **静电防护**：操作时注意静电防护
3. **信号质量**：使用高质量的信号源和连接线
4. **环境条件**：在稳定的温度和湿度环境下测试
