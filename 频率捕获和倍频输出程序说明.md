# STM32F4 频率捕获和5倍频输出程序

## 功能描述
本程序实现了以下功能：
1. **PA3引脚输入捕获**：捕获外部信号的频率
2. **PE3引脚PWM输出**：输出捕获频率的5倍频信号
3. **实时监控**：通过串口输出频率信息

## 硬件连接
- **PA3**：输入捕获引脚，连接待测频率信号
- **PE3**：PWM输出引脚，输出5倍频信号
- **串口**：用于调试信息输出

## 技术实现

### 1. 输入捕获 (TIM2)
- 使用TIM2的通道4 (PA3)进行输入捕获
- 配置为上升沿触发
- 定时器频率：1MHz (84MHz/84)
- 通过测量两次捕获之间的时间间隔计算输入频率

### 2. PWM输出 (TIM9)
- 使用TIM9的通道2 (PE3)输出PWM信号
- 定时器频率：1MHz (168MHz/168)
- 根据捕获到的频率动态调整PWM频率为5倍
- 占空比固定为50%

### 3. 中断处理
- TIM2捕获中断处理频率计算
- 实时更新PWM输出频率

## 代码结构

### 修改的文件：
1. **TIM.h** - 添加了新函数声明和全局变量
2. **TIM.c** - 实现了频率捕获和PWM输出功能
3. **main.cpp** - 在主函数中初始化频率捕获功能
4. **task_test.cpp** - 在测试任务中添加频率监控
5. **task_test.h** - 添加频率监控函数声明
6. **stm32f4xx_it.c** - 添加TIM2中断服务函数

### 主要函数：
- `FrequencyCapture_Init()` - 初始化频率捕获和PWM输出
- `TIM2_InputCapture_Init()` - 初始化PA3输入捕获
- `TIM9_PWM_Init()` - 初始化PE3 PWM输出
- `TIM2_Capture_Process()` - 处理频率计算
- `FrequencyMonitor()` - 监控和显示频率信息

## 使用方法

1. **编译和下载**：编译项目并下载到STM32F407开发板
2. **连接信号**：将待测频率信号连接到PA3引脚
3. **观察输出**：
   - PE3引脚输出5倍频PWM信号
   - 串口输出频率信息（每秒更新一次）

## 技术参数

- **输入频率范围**：理论上支持1Hz到几十kHz
- **输出频率限制**：最大100kHz（可调整）
- **测量精度**：取决于输入信号稳定性和定时器精度
- **响应时间**：约2个输入信号周期

## 注意事项

1. **信号电平**：输入信号应为3.3V TTL电平
2. **频率限制**：输出频率被限制在100kHz以下，防止系统过载
3. **信号质量**：输入信号应有良好的边沿，避免噪声干扰
4. **电源稳定**：确保开发板电源稳定，避免影响定时器精度

## 调试信息

程序通过串口输出以下信息：
- 输入频率值
- 输出频率值（5倍频）
- 异常状态提示

示例输出：
```
Input Frequency: 1000 Hz, Output Frequency: 5000 Hz (5x)
Input Frequency: 2500 Hz, Output Frequency: 12500 Hz (5x)
Warning: No input signal detected on PA3
```

## 扩展功能

可以根据需要扩展以下功能：
1. 调整倍频系数
2. 添加频率滤波
3. 支持更宽的频率范围
4. 添加频率显示界面
5. 保存频率历史数据
